# Track Tasks API Configuration

# API Server Configuration
API_PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration (PocketBase)
POCKETBASE_URL=http://localhost:8090
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your-pocketbase-password

# Security Configuration
JWT_SECRET=your-jwt-secret-key-change-in-production
API_KEY=your-api-key-for-programmatic-access

# Development/Testing
DEBUG=true
LOG_LEVEL=info

# Trace Logging Configuration
ENABLE_TRACE_LOGGING=false
TRACE_LOG_LEVEL=debug
TRACE_LOG_FORMAT=json
