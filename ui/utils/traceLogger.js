/**
 * UI Trace Logger Utility
 * Provides comprehensive trace logging for Vue.js components and client-side operations
 */

/**
 * Get trace logging configuration from environment
 */
function getTraceConfig() {
  // In Vite environment (browser)
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return {
      enabled: import.meta.env.VITE_ENABLE_TRACE_LOGGING === 'true',
      level: import.meta.env.VITE_TRACE_LOG_LEVEL || 'debug',
      format: import.meta.env.VITE_TRACE_LOG_FORMAT || 'json'
    }
  }
  
  // Fallback for testing or other environments
  return {
    enabled: false,
    level: 'debug',
    format: 'json'
  }
}

/**
 * Log levels for filtering
 */
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
  trace: 4
}

/**
 * Get current log level number
 */
function getCurrentLogLevel() {
  const config = getTraceConfig()
  return LOG_LEVELS[config.level] || LOG_LEVELS.debug
}

/**
 * Check if trace logging is enabled and level is appropriate
 * @param {string} level - Log level to check
 * @returns {boolean} Whether logging should occur
 */
function shouldLog(level = 'debug') {
  const config = getTraceConfig()
  if (!config.enabled) return false
  return LOG_LEVELS[level] <= getCurrentLogLevel()
}

/**
 * Format trace log entry
 * @param {string} type - Log type (ENTRY, EXIT, HTTP_REQUEST, etc.)
 * @param {string} hierarchy - Hierarchy notation (-> or --> or --->)
 * @param {string} name - Function/component name
 * @param {Object} data - Additional data to log
 * @param {string} level - Log level
 * @returns {Object} Formatted log entry
 */
function formatTraceLog(type, hierarchy, name, data = {}, level = 'debug') {
  const timestamp = new Date().toISOString()
  const config = getTraceConfig()
  
  const logEntry = {
    timestamp,
    type: `UI_TRACE_${type}`,
    level: level.toUpperCase(),
    hierarchy,
    name,
    ...data
  }

  if (config.format === 'json') {
    return JSON.stringify(logEntry)
  } else {
    // Simple text format
    const dataStr = Object.keys(data).length > 0 ? ` | ${JSON.stringify(data)}` : ''
    return `[${timestamp}] UI_TRACE_${type} ${hierarchy} ${name}${dataStr}`
  }
}

/**
 * Core trace logging function
 * @param {string} type - Log type
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} name - Function/component name
 * @param {Object} data - Additional data
 * @param {string} level - Log level
 */
function trace(type, hierarchy, name, data = {}, level = 'debug') {
  if (!shouldLog(level)) return

  const logMessage = formatTraceLog(type, hierarchy, name, data, level)
  
  // Use appropriate console method based on level
  switch (level) {
    case 'error':
      console.error(logMessage)
      break
    case 'warn':
      console.warn(logMessage)
      break
    case 'info':
      console.info(logMessage)
      break
    default:
      console.log(logMessage)
  }
}

/**
 * Trace Vue component lifecycle
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} componentName - Component name
 * @param {string} lifecycle - Lifecycle hook name
 * @param {Object} props - Component props
 * @param {Object} data - Component data
 */
function traceVueLifecycle(hierarchy, componentName, lifecycle, props = {}, data = {}) {
  trace('VUE_LIFECYCLE', hierarchy, `${componentName}.${lifecycle}`, {
    component: componentName,
    lifecycle,
    props: sanitizeData(props),
    data: sanitizeData(data)
  })
}

/**
 * Trace function entry
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} functionName - Name of the function being entered
 * @param {Object} args - Function arguments
 * @param {Object} context - Additional context
 */
function traceEntry(hierarchy, functionName, args = {}, context = {}) {
  trace('ENTRY', hierarchy, functionName, {
    args: sanitizeData(args),
    context: sanitizeData(context)
  })
}

/**
 * Trace function exit
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} functionName - Name of the function being exited
 * @param {Object} result - Function result
 * @param {number} duration - Execution duration in ms
 */
function traceExit(hierarchy, functionName, result = {}, duration = null) {
  const data = { result: sanitizeData(result) }
  if (duration !== null) {
    data.duration_ms = duration
  }
  
  trace('EXIT', hierarchy, functionName, data)
}

/**
 * Trace HTTP request (client-side)
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {Object} headers - Request headers
 * @param {Object} body - Request body
 * @param {string} requestId - Request ID for correlation
 */
function traceHttpRequest(hierarchy, method, url, headers = {}, body = {}, requestId = null) {
  trace('HTTP_REQUEST', hierarchy, `${method} ${url}`, {
    method,
    url,
    headers: sanitizeHeaders(headers),
    body: sanitizeData(body),
    request_id: requestId
  })
}

/**
 * Trace HTTP response (client-side)
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {number} statusCode - Response status code
 * @param {Object} headers - Response headers
 * @param {Object} body - Response body
 * @param {number} duration - Request duration in ms
 * @param {string} requestId - Request ID for correlation
 */
function traceHttpResponse(hierarchy, method, url, statusCode, headers = {}, body = {}, duration = null, requestId = null) {
  const data = {
    method,
    url,
    status_code: statusCode,
    headers: sanitizeHeaders(headers),
    body: sanitizeData(body),
    request_id: requestId
  }
  
  if (duration !== null) {
    data.duration_ms = duration
  }
  
  trace('HTTP_RESPONSE', hierarchy, `${method} ${url}`, data)
}

/**
 * Trace Vue store action
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} storeName - Store name
 * @param {string} actionName - Action name
 * @param {Object} payload - Action payload
 * @param {Object} state - Current state (optional)
 */
function traceStoreAction(hierarchy, storeName, actionName, payload = {}, state = {}) {
  trace('STORE_ACTION', hierarchy, `${storeName}.${actionName}`, {
    store: storeName,
    action: actionName,
    payload: sanitizeData(payload),
    state: sanitizeData(state)
  })
}

/**
 * Trace error
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} context - Error context
 * @param {Error} error - Error object
 * @param {Object} additionalData - Additional error context
 */
function traceError(hierarchy, context, error, additionalData = {}) {
  trace('ERROR', hierarchy, context, {
    error_message: error.message,
    error_name: error.name,
    error_stack: error.stack,
    ...sanitizeData(additionalData)
  }, 'error')
}

/**
 * Sanitize headers for logging (remove sensitive data)
 * @param {Object} headers - Headers object
 * @returns {Object} Sanitized headers
 */
function sanitizeHeaders(headers) {
  if (!headers || typeof headers !== 'object') return headers
  
  const sanitized = { ...headers }
  const sensitiveHeaders = ['authorization', 'cookie', 'x-csrf-token', 'x-api-key']
  
  sensitiveHeaders.forEach(header => {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]'
    }
    if (sanitized[header.toLowerCase()]) {
      sanitized[header.toLowerCase()] = '[REDACTED]'
    }
  })
  
  return sanitized
}

/**
 * Sanitize data for logging (remove sensitive data)
 * @param {Object} data - Data object
 * @returns {Object} Sanitized data
 */
function sanitizeData(data) {
  if (!data || typeof data !== 'object') return data
  
  const sanitized = { ...data }
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'refreshToken']
  
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]'
    }
  })
  
  return sanitized
}

/**
 * Create a trace wrapper for async functions
 * @param {string} hierarchy - Hierarchy notation
 * @param {string} functionName - Function name
 * @param {Function} fn - Function to wrap
 * @returns {Function} Wrapped function
 */
function traceWrapper(hierarchy, functionName, fn) {
  return async function(...args) {
    const startTime = Date.now()
    
    traceEntry(hierarchy, functionName, { args })
    
    try {
      const result = await fn.apply(this, args)
      const duration = Date.now() - startTime
      traceExit(hierarchy, functionName, { result }, duration)
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      traceError(hierarchy, functionName, error, { duration_ms: duration })
      throw error
    }
  }
}

export {
  trace,
  traceEntry,
  traceExit,
  traceHttpRequest,
  traceHttpResponse,
  traceVueLifecycle,
  traceStoreAction,
  traceError,
  traceWrapper,
  getTraceConfig,
  shouldLog
}
