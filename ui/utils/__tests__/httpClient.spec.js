/**
 * HTTP Client Unit Tests
 * Tests for HTTP client utility functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import httpClient, { HTTPError, HTTP_ERRORS } from '../httpClient.js'

// Mock fetch
global.fetch = vi.fn()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('HTTP Client', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('HTTPError', () => {
    it('should create HTTPError with correct properties', () => {
      const error = new HTTPError('Test error', 400, HTTP_ERRORS.VALIDATION_ERROR, 'Test details')
      
      expect(error.message).toBe('Test error')
      expect(error.statusCode).toBe(400)
      expect(error.errorType).toBe(HTTP_ERRORS.VALIDATION_ERROR)
      expect(error.details).toBe('Test details')
      expect(error.name).toBe('HTTPError')
      expect(error.timestamp).toBeDefined()
    })
  })

  describe('Basic HTTP Methods', () => {
    it('should make GET request successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true, data: 'test' })
      }
      
      fetch.mockResolvedValue(mockResponse)

      const result = await httpClient.get('/test')

      expect(fetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        })
      }))
      expect(result).toEqual({ success: true, data: 'test' })
    })

    it('should make POST request successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true, data: 'created' })
      }
      
      fetch.mockResolvedValue(mockResponse)

      const testData = { name: 'test' }
      const result = await httpClient.post('/test', testData)

      expect(fetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'POST',
        body: JSON.stringify(testData),
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        })
      }))
      expect(result).toEqual({ success: true, data: 'created' })
    })

    it('should make PUT request successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true, data: 'updated' })
      }
      
      fetch.mockResolvedValue(mockResponse)

      const testData = { name: 'updated' }
      const result = await httpClient.put('/test/1', testData)

      expect(fetch).toHaveBeenCalledWith('/api/test/1', expect.objectContaining({
        method: 'PUT',
        body: JSON.stringify(testData)
      }))
      expect(result).toEqual({ success: true, data: 'updated' })
    })

    it('should make DELETE request successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true })
      }
      
      fetch.mockResolvedValue(mockResponse)

      const result = await httpClient.delete('/test/1')

      expect(fetch).toHaveBeenCalledWith('/api/test/1', expect.objectContaining({
        method: 'DELETE'
      }))
      expect(result).toEqual({ success: true })
    })
  })

  describe('Authentication', () => {
    it('should add authorization header when token is available', async () => {
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'test-token'
        return null
      })

      const mockResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true })
      }
      
      fetch.mockResolvedValue(mockResponse)

      await httpClient.get('/test')

      expect(fetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': 'Bearer test-token'
        })
      }))
    })

    it('should not add authorization header when no token is available', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true })
      }
      
      fetch.mockResolvedValue(mockResponse)

      await httpClient.get('/test')

      expect(fetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        headers: expect.not.objectContaining({
          'Authorization': expect.any(String)
        })
      }))
    })
  })

  describe('CSRF Token Handling', () => {
    it('should add CSRF token for POST requests', async () => {
      // Mock CSRF token fetch
      const csrfResponse = {
        ok: true,
        json: vi.fn().mockResolvedValue({
          data: { csrfToken: 'csrf-token' }
        })
      }
      
      const mainResponse = {
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true })
      }
      
      fetch
        .mockResolvedValueOnce(csrfResponse) // First call for CSRF token
        .mockResolvedValueOnce(mainResponse) // Second call for actual request

      await httpClient.post('/test', { data: 'test' })

      expect(fetch).toHaveBeenCalledTimes(2)
      expect(fetch).toHaveBeenNthCalledWith(1, '/api/auth/csrf-token', expect.objectContaining({
        method: 'GET',
        credentials: 'include'
      }))
      expect(fetch).toHaveBeenNthCalledWith(2, '/api/test', expect.objectContaining({
        headers: expect.objectContaining({
          'X-CSRF-Token': 'csrf-token'
        })
      }))
    })
  })

  describe('Error Handling', () => {
    it('should handle 400 validation errors', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({
          error: {
            message: 'Validation failed',
            details: 'Email is required'
          }
        })
      }
      
      fetch.mockResolvedValue(mockResponse)

      await expect(httpClient.get('/test')).rejects.toThrow(HTTPError)
      
      try {
        await httpClient.get('/test')
      } catch (error) {
        expect(error.statusCode).toBe(400)
        expect(error.errorType).toBe(HTTP_ERRORS.VALIDATION_ERROR)
        expect(error.message).toBe('Validation failed')
        expect(error.details).toBe('Email is required')
      }
    })

    it('should handle 401 authentication errors', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({
          error: {
            message: 'Unauthorized'
          }
        })
      }
      
      fetch.mockResolvedValue(mockResponse)

      await expect(httpClient.get('/test')).rejects.toThrow(HTTPError)
      
      try {
        await httpClient.get('/test')
      } catch (error) {
        expect(error.statusCode).toBe(401)
        expect(error.errorType).toBe(HTTP_ERRORS.AUTHENTICATION_ERROR)
      }
    })

    it('should handle 429 rate limit errors', async () => {
      const mockResponse = {
        ok: false,
        status: 429,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({
          error: {
            message: 'Too many requests'
          }
        })
      }
      
      fetch.mockResolvedValue(mockResponse)

      await expect(httpClient.get('/test')).rejects.toThrow(HTTPError)
      
      try {
        await httpClient.get('/test')
      } catch (error) {
        expect(error.statusCode).toBe(429)
        expect(error.errorType).toBe(HTTP_ERRORS.RATE_LIMIT_ERROR)
      }
    })

    it('should handle 500 server errors', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({
          error: {
            message: 'Internal server error'
          }
        })
      }
      
      fetch.mockResolvedValue(mockResponse)

      await expect(httpClient.get('/test')).rejects.toThrow(HTTPError)
      
      try {
        await httpClient.get('/test')
      } catch (error) {
        expect(error.statusCode).toBe(500)
        expect(error.errorType).toBe(HTTP_ERRORS.SERVER_ERROR)
      }
    })

    it('should handle network errors', async () => {
      fetch.mockRejectedValue(new TypeError('Failed to fetch'))

      await expect(httpClient.get('/test')).rejects.toThrow(HTTPError)
      
      try {
        await httpClient.get('/test')
      } catch (error) {
        expect(error.errorType).toBe(HTTP_ERRORS.NETWORK_ERROR)
        expect(error.message).toBe('Network error')
      }
    })

    it('should handle timeout errors', async () => {
      // Mock AbortError
      const abortError = new Error('The operation was aborted')
      abortError.name = 'AbortError'
      
      fetch.mockRejectedValue(abortError)

      await expect(httpClient.get('/test')).rejects.toThrow(HTTPError)
      
      try {
        await httpClient.get('/test')
      } catch (error) {
        expect(error.errorType).toBe(HTTP_ERRORS.TIMEOUT_ERROR)
        expect(error.message).toBe('Request timeout')
      }
    })
  })

  describe('Token Management', () => {
    it('should get auth token from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('test-token')
      
      const token = httpClient.getAuthToken()
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('auth_token')
      expect(token).toBe('test-token')
    })

    it('should return null when no token in localStorage', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const token = httpClient.getAuthToken()
      
      expect(token).toBeNull()
    })

    it('should clear auth state', () => {
      httpClient.clearAuthState()
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_refresh_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
    })
  })
})
