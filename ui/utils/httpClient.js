/**
 * HTTP Client Utility
 * Provides a centralized HTTP client with authentication, CSRF protection, and error handling
 */

import { traceEntry, traceExit, traceHttpRequest, traceHttpResponse, traceError } from './traceLogger.js'

/**
 * Get API base URL from environment
 */
function getApiBaseUrl() {
  // In Vite environment (browser)
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
  }
  // In Node.js environment (testing)
  if (typeof process !== 'undefined' && process.env) {
    return process.env.VITE_API_BASE_URL || 'http://localhost:3001'
  }
  // Fallback
  return 'http://localhost:3001'
}

/**
 * HTTP Client Configuration
 */
const CONFIG = {
  BASE_URL: `${getApiBaseUrl()}/api`,
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
}

/**
 * Error types for different HTTP scenarios
 */
export const HTTP_ERRORS = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
}

/**
 * Custom HTTP Error class
 */
export class HTTPError extends Error {
  constructor(message, statusCode, errorType, details = null) {
    super(message)
    this.name = 'HTTPError'
    this.statusCode = statusCode
    this.errorType = errorType
    this.details = details
    this.timestamp = new Date().toISOString()
  }
}

/**
 * HTTP Client class with authentication and error handling
 */
class HTTPClient {
  constructor() {
    this.baseURL = CONFIG.BASE_URL
    this.timeout = CONFIG.TIMEOUT
    this.retryAttempts = CONFIG.RETRY_ATTEMPTS
    this.retryDelay = CONFIG.RETRY_DELAY

    // Request interceptors
    this.requestInterceptors = []
    this.responseInterceptors = []

    // Setup default interceptors
    this.setupDefaultInterceptors()
  }

  /**
   * Setup default request and response interceptors
   */
  setupDefaultInterceptors() {
    // Request interceptor for authentication
    this.addRequestInterceptor(async (config) => {
      // Add authentication token if available
      const token = this.getAuthToken()
      if (token) {
        config.headers = {
          ...config.headers,
          'Authorization': `Bearer ${token}`
        }
      }

      // Add CSRF token for state-changing requests
      if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(config.method?.toUpperCase())) {
        const csrfToken = await this.getCSRFToken()
        if (csrfToken) {
          config.headers = {
            ...config.headers,
            'X-CSRF-Token': csrfToken
          }
        }
      }

      // Add default headers
      config.headers = {
        'Content-Type': 'application/json',
        ...config.headers
      }

      return config
    })

    // Response interceptor for error handling and token refresh
    this.addResponseInterceptor(
      (response) => response, // Success handler
      async (error) => {
        const originalRequest = error.config

        // Handle 401 Unauthorized - attempt token refresh
        if (error.statusCode === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            const refreshed = await this.refreshAuthToken()
            if (refreshed) {
              // Retry the original request with new token
              const token = this.getAuthToken()
              originalRequest.headers['Authorization'] = `Bearer ${token}`
              return this.request(originalRequest.url, originalRequest)
            }
          } catch (refreshError) {
            // Refresh failed, handle token expiration
            this.handleTokenExpiration()
            throw error
          }
        }

        throw error
      }
    )
  }

  /**
   * Add request interceptor
   * @param {Function} interceptor - Request interceptor function
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * Add response interceptor
   * @param {Function} successHandler - Success response handler
   * @param {Function} errorHandler - Error response handler
   */
  addResponseInterceptor(successHandler, errorHandler) {
    this.responseInterceptors.push({ successHandler, errorHandler })
  }

  /**
   * Apply request interceptors to config
   * @param {Object} config - Request configuration
   * @returns {Promise<Object>} Modified configuration
   */
  async applyRequestInterceptors(config) {
    let modifiedConfig = { ...config }

    for (const interceptor of this.requestInterceptors) {
      modifiedConfig = await interceptor(modifiedConfig)
    }

    return modifiedConfig
  }

  /**
   * Apply response interceptors
   * @param {Response|Error} responseOrError - Response or error object
   * @param {boolean} isError - Whether this is an error response
   * @returns {Promise<Response>} Processed response
   */
  async applyResponseInterceptors(responseOrError, isError = false) {
    let result = responseOrError

    for (const { successHandler, errorHandler } of this.responseInterceptors) {
      try {
        if (isError && errorHandler) {
          result = await errorHandler(result)
        } else if (!isError && successHandler) {
          result = await successHandler(result)
        }
      } catch (interceptorError) {
        result = interceptorError
        isError = true
      }
    }

    return result
  }

  /**
   * Get authentication token from localStorage
   * @returns {string|null} Authentication token
   */
  getAuthToken() {
    if (typeof window !== 'undefined' && window.localStorage) {
      return localStorage.getItem('auth_token')
    }
    return null
  }

  /**
   * Get CSRF token from API
   * @returns {Promise<string>} CSRF token
   */
  async getCSRFToken() {
    try {
      const response = await fetch(`${this.baseURL}/auth/csrf-token`, {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        return data.data?.csrfToken || ''
      }
    } catch (error) {
      console.warn('Failed to get CSRF token:', error)
    }

    return ''
  }

  /**
   * Refresh authentication token
   * @returns {Promise<boolean>} Success status
   */
  async refreshAuthToken() {
    try {
      // Get refresh token from localStorage
      const refreshToken = localStorage.getItem('auth_refresh_token')
      if (!refreshToken) {
        return false
      }

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refreshToken })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data.token) {
          localStorage.setItem('auth_token', data.data.token)
          if (data.data.refreshToken) {
            localStorage.setItem('auth_refresh_token', data.data.refreshToken)
          }
          return true
        }
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
    }

    return false
  }

  /**
   * Clear authentication state
   */
  clearAuthState() {
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_refresh_token')
      localStorage.removeItem('auth_user')
    }
  }

  /**
   * Handle token expiration by clearing auth state and redirecting to login
   */
  handleTokenExpiration() {
    this.clearAuthState()

    // If we're in a browser environment, redirect to login
    if (typeof window !== 'undefined' && window.location) {
      const currentPath = window.location.pathname + window.location.search
      const loginUrl = `/login${currentPath !== '/login' ? `?redirect=${encodeURIComponent(currentPath)}` : ''}`
      window.location.href = loginUrl
    }
  }

  /**
   * Create HTTP error from response
   * @param {Response} response - Fetch response object
   * @param {Object} data - Response data
   * @returns {HTTPError} HTTP error instance
   */
  createHTTPError(response, data) {
    const { status } = response
    let errorType = HTTP_ERRORS.UNKNOWN_ERROR
    let message = 'An unknown error occurred'
    let details = null

    if (data && data.error) {
      message = data.error.message || message
      details = data.error.details || data.error.code || null
    }

    // Determine error type based on status code
    switch (status) {
      case 400:
        errorType = HTTP_ERRORS.VALIDATION_ERROR
        break
      case 401:
        errorType = HTTP_ERRORS.AUTHENTICATION_ERROR
        break
      case 403:
        errorType = HTTP_ERRORS.AUTHORIZATION_ERROR
        break
      case 429:
        errorType = HTTP_ERRORS.RATE_LIMIT_ERROR
        break
      case 500:
      case 502:
      case 503:
      case 504:
        errorType = HTTP_ERRORS.SERVER_ERROR
        break
    }

    return new HTTPError(message, status, errorType, details)
  }

  /**
   * Make HTTP request with retry logic
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @param {number} attempt - Current attempt number
   * @returns {Promise<Object>} Response data
   */
  async request(url, options = {}, attempt = 1) {
    const startTime = Date.now()

    // Prepare request configuration
    const config = {
      method: 'GET',
      headers: {},
      ...options,
      url: url.startsWith('http') ? url : `${this.baseURL}${url}`
    }

    // Trace HTTP request
    traceHttpRequest('--> ', config.method, config.url, config.headers, config.body)
    traceEntry('--> ', 'HttpClient.request', { method: config.method, url: config.url, attempt })

    try {
      // Apply request interceptors
      const modifiedConfig = await this.applyRequestInterceptors(config)

      // Create AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)

      // Make the request
      const response = await fetch(modifiedConfig.url, {
        ...modifiedConfig,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      // Parse response
      let data = null
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      }

      const duration = Date.now() - startTime

      // Handle error responses
      if (!response.ok) {
        // Trace error response
        traceHttpResponse('--> ', config.method, config.url, response.status, Object.fromEntries(response.headers), data, duration)
        traceError('--> ', 'HttpClient.request', new Error(`HTTP ${response.status}`), {
          status: response.status,
          duration_ms: duration,
          url: config.url
        })

        const error = this.createHTTPError(response, data)
        error.config = modifiedConfig

        // Apply error response interceptors
        throw await this.applyResponseInterceptors(error, true)
      }

      // Trace successful response
      traceHttpResponse('--> ', config.method, config.url, response.status, Object.fromEntries(response.headers), data, duration)
      traceExit('--> ', 'HttpClient.request', { status: response.status, success: true }, duration)

      // Apply success response interceptors
      const processedResponse = await this.applyResponseInterceptors({ response, data })
      return processedResponse.data || data

    } catch (error) {
      const duration = Date.now() - startTime

      // Trace network errors
      traceError('--> ', 'HttpClient.request', error, {
        duration_ms: duration,
        attempt,
        url: config.url,
        method: config.method
      })

      // Handle network errors
      if (error.name === 'AbortError') {
        throw new HTTPError('Request timeout', 0, HTTP_ERRORS.TIMEOUT_ERROR)
      }

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new HTTPError('Network error', 0, HTTP_ERRORS.NETWORK_ERROR)
      }

      // Retry logic for certain errors
      if (attempt < this.retryAttempts && this.shouldRetry(error)) {
        traceEntry('--> ', 'HttpClient.request.retry', { attempt: attempt + 1, url: config.url })
        await this.delay(this.retryDelay * attempt)
        return this.request(url, options, attempt + 1)
      }

      throw error
    }
  }

  /**
   * Determine if request should be retried
   * @param {Error} error - Error object
   * @returns {boolean} Whether to retry
   */
  shouldRetry(error) {
    // Retry on network errors and 5xx server errors
    return error.errorType === HTTP_ERRORS.NETWORK_ERROR ||
           error.errorType === HTTP_ERRORS.TIMEOUT_ERROR ||
           (error.statusCode >= 500 && error.statusCode < 600)
  }

  /**
   * Delay execution
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Convenience methods for different HTTP verbs
  get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' })
  }

  post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  patch(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PATCH',
      body: JSON.stringify(data)
    })
  }

  delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' })
  }
}

// Create singleton instance
const httpClient = new HTTPClient()

export default httpClient
export { httpClient }
