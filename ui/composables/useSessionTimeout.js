/**
 * Session Timeout Composable
 * Handles session timeout detection, warnings, and automatic logout
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { useRouter } from 'vue-router'

/**
 * Session timeout configuration
 */
const SESSION_CONFIG = {
  // Session timeout in milliseconds (30 minutes default)
  TIMEOUT_DURATION: 30 * 60 * 1000,

  // Warning threshold in milliseconds (5 minutes before timeout)
  WARNING_THRESHOLD: 5 * 60 * 1000,

  // Activity check interval in milliseconds
  CHECK_INTERVAL: 30 * 1000, // 30 seconds

  // Grace period for session extension in milliseconds
  GRACE_PERIOD: 2 * 60 * 1000, // 2 minutes

  // Local storage key for cross-tab communication
  STORAGE_KEY: 'session_activity',

  // Events that count as user activity
  ACTIVITY_EVENTS: [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ]
}

/**
 * Session timeout composable
 * @param {Object} options - Configuration options
 * @returns {Object} Session timeout state and methods
 */
export function useSessionTimeout(options = {}) {
  const authStore = useAuthStore()
  const router = useRouter()

  // Merge options with defaults
  const config = { ...SESSION_CONFIG, ...options }

  // Reactive state
  const lastActivity = ref(Date.now())
  const showWarning = ref(false)
  const timeRemaining = ref(0)
  const isActive = ref(false)

  // Timers
  let activityTimer = null
  let warningTimer = null
  let logoutTimer = null
  let checkInterval = null

  // Track registered event listeners for testing
  const registeredEventListeners = new Map()

  /**
   * Computed properties
   */
  const timeUntilWarning = computed(() => {
    const elapsed = Date.now() - lastActivity.value
    return Math.max(0, config.TIMEOUT_DURATION - config.WARNING_THRESHOLD - elapsed)
  })

  const timeUntilLogout = computed(() => {
    const elapsed = Date.now() - lastActivity.value
    return Math.max(0, config.TIMEOUT_DURATION - elapsed)
  })

  const isNearTimeout = computed(() => {
    return timeUntilLogout.value <= config.WARNING_THRESHOLD
  })

  const formattedTimeRemaining = computed(() => {
    const minutes = Math.floor(timeRemaining.value / 60000)
    const seconds = Math.floor((timeRemaining.value % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  })

  /**
   * Update last activity timestamp
   */
  const updateActivity = () => {
    const now = Date.now()
    lastActivity.value = now

    // Update cross-tab communication
    try {
      localStorage.setItem(config.STORAGE_KEY, now.toString())
    } catch (error) {
      console.warn('Failed to update activity in localStorage:', error)
    }

    // Reset timers if warning is showing
    if (showWarning.value) {
      hideWarning()
      scheduleWarning()
    }
  }

  /**
   * Handle user activity events
   */
  const handleActivity = () => {
    if (!authStore.isAuthenticated) return

    updateActivity()
  }

  /**
   * Schedule warning timer
   */
  const scheduleWarning = () => {
    clearTimeout(warningTimer)

    const timeUntilWarningMs = timeUntilWarning.value

    if (timeUntilWarningMs > 0) {
      warningTimer = setTimeout(() => {
        if (authStore.isAuthenticated) {
          showSessionWarning()
        }
      }, timeUntilWarningMs)
    }
  }

  /**
   * Show session timeout warning
   */
  const showSessionWarning = () => {
    if (!authStore.isAuthenticated) return

    showWarning.value = true
    startCountdown()

    // Schedule automatic logout
    clearTimeout(logoutTimer)
    logoutTimer = setTimeout(() => {
      handleSessionTimeout()
    }, config.WARNING_THRESHOLD)
  }

  /**
   * Hide session timeout warning
   */
  const hideWarning = () => {
    showWarning.value = false
    timeRemaining.value = 0
    clearTimeout(logoutTimer)
  }

  /**
   * Start countdown timer for warning
   */
  const startCountdown = () => {
    const updateCountdown = () => {
      if (!showWarning.value) return

      const remaining = timeUntilLogout.value
      timeRemaining.value = remaining

      if (remaining <= 0) {
        handleSessionTimeout()
      } else {
        setTimeout(updateCountdown, 1000)
      }
    }

    updateCountdown()
  }

  /**
   * Extend session (user clicked extend button)
   */
  const extendSession = async () => {
    try {
      // Call API to extend session
      const response = await fetch('/api/auth/extend-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        }
      })

      if (response.ok) {
        updateActivity()
        hideWarning()
        scheduleWarning()

        return { success: true }
      } else {
        throw new Error('Failed to extend session')
      }
    } catch (error) {
      console.error('Session extension failed:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Handle session timeout
   */
  const handleSessionTimeout = async () => {
    hideWarning()

    try {
      // Logout user
      await authStore.logout()

      // Redirect to login with timeout message
      router.push({
        name: 'login',
        query: { reason: 'timeout' }
      })
    } catch (error) {
      console.error('Logout failed during timeout:', error)
      // Force redirect even if logout fails
      router.push({ name: 'login' })
    }
  }

  /**
   * Check for cross-tab activity
   */
  const checkCrossTabActivity = () => {
    try {
      const storedActivity = localStorage.getItem(config.STORAGE_KEY)
      if (storedActivity) {
        const storedTime = parseInt(storedActivity, 10)
        if (storedTime > lastActivity.value) {
          lastActivity.value = storedTime

          // Hide warning if activity detected in another tab
          if (showWarning.value) {
            hideWarning()
            scheduleWarning()
          }
        }
      }
    } catch (error) {
      console.warn('Failed to check cross-tab activity:', error)
    }
  }

  /**
   * Start session timeout monitoring
   */
  const startMonitoring = () => {
    if (!authStore.isAuthenticated) return

    // Initialize activity timestamp
    updateActivity()

    // Add activity event listeners
    config.ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true })

      // Track for testing
      if (!registeredEventListeners.has(event)) {
        registeredEventListeners.set(event, [])
      }
      registeredEventListeners.get(event).push(handleActivity)
    })

    // Start periodic checks for cross-tab activity
    checkInterval = setInterval(() => {
      checkCrossTabActivity()

      // Check if we should show warning
      if (!showWarning.value && isNearTimeout.value && authStore.isAuthenticated) {
        showSessionWarning()
      }
    }, config.CHECK_INTERVAL)

    // Schedule initial warning
    scheduleWarning()

    isActive.value = true
  }

  /**
   * Stop session timeout monitoring
   */
  const stopMonitoring = () => {
    // Remove event listeners
    config.ACTIVITY_EVENTS.forEach(event => {
      document.removeEventListener(event, handleActivity)
    })

    // Clear tracked listeners
    registeredEventListeners.clear()

    // Clear timers
    clearTimeout(warningTimer)
    clearTimeout(logoutTimer)
    clearInterval(checkInterval)

    // Hide warning
    hideWarning()

    isActive.value = false
  }

  /**
   * Reset session timeout
   */
  const resetTimeout = () => {
    updateActivity()
    hideWarning()
    scheduleWarning()
  }

  /**
   * Get session status
   */
  const getSessionStatus = () => {
    return {
      isActive: isActive.value,
      lastActivity: lastActivity.value,
      timeUntilWarning: timeUntilWarning.value,
      timeUntilLogout: timeUntilLogout.value,
      showWarning: showWarning.value,
      timeRemaining: timeRemaining.value,
      formattedTimeRemaining: formattedTimeRemaining.value
    }
  }

  // Lifecycle hooks - removed auto-start to let components control when to start
  onMounted(() => {
    // Components should call startMonitoring() explicitly when needed
  })

  onUnmounted(() => {
    stopMonitoring()
  })

  // Watch for authentication changes
  authStore.$subscribe((mutation, state) => {
    if (state.isAuthenticated && !isActive.value) {
      startMonitoring()
    } else if (!state.isAuthenticated && isActive.value) {
      stopMonitoring()
    }
  })

  return {
    // State
    showWarning: computed(() => showWarning.value),
    timeRemaining: computed(() => timeRemaining.value),
    formattedTimeRemaining,
    isNearTimeout,
    timeUntilWarning,
    timeUntilLogout,
    isActive: computed(() => isActive.value),

    // Methods
    extendSession,
    resetTimeout,
    startMonitoring,
    stopMonitoring,
    getSessionStatus,

    // Internal methods (for testing)
    updateActivity,
    handleSessionTimeout,

    // For testing event listener registration
    get eventListeners() {
      return registeredEventListeners
    }
  }
}
