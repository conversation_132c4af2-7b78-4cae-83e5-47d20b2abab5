/**
 * Session Management Composable
 * Handles fetching, managing, and monitoring user sessions
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'

/**
 * Session management composable
 * @returns {Object} Session management state and methods
 */
export function useSessionManagement() {
  const authStore = useAuthStore()

  // Reactive state
  const sessions = ref([])
  const loading = ref(false)
  const error = ref(null)
  const refreshInterval = ref(null)

  // Configuration
  const REFRESH_INTERVAL = 30000 // 30 seconds

  /**
   * Computed properties
   */
  const activeSessions = computed(() => {
    return sessions.value.filter(session => session.isActive)
  })

  const currentSession = computed(() => {
    return sessions.value.find(session => session.isCurrent)
  })

  const otherSessions = computed(() => {
    return sessions.value.filter(session => !session.isCurrent && session.isActive)
  })

  const totalSessions = computed(() => {
    return activeSessions.value.length
  })

  const hasMultipleSessions = computed(() => {
    return totalSessions.value > 1
  })

  /**
   * Fetch user sessions from API
   */
  const fetchSessions = async () => {
    if (!authStore.isAuthenticated) {
      return
    }

    loading.value = true
    error.value = null

    try {
      const response = await fetch('/api/auth/sessions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`,
          'X-CSRF-Token': await getCsrfToken()
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        sessions.value = data.data.sessions || []
      } else {
        throw new Error(data.error?.message || 'Failed to fetch sessions')
      }
    } catch (err) {
      error.value = err.message
      console.error('Failed to fetch sessions:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * Terminate a specific session
   * @param {string} sessionId - Session ID to terminate
   */
  const terminateSession = async (sessionId) => {
    if (!authStore.isAuthenticated) {
      throw new Error('Not authenticated')
    }

    try {
      const response = await fetch(`/api/auth/sessions/${sessionId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`,
          'X-CSRF-Token': await getCsrfToken()
        }
      })

      // Add defensive check for undefined response
      if (!response) {
        throw new Error('No response received from server')
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        // Remove session from local state
        sessions.value = sessions.value.filter(session => session.id !== sessionId)
        return data
      } else {
        throw new Error(data.error?.message || 'Failed to terminate session')
      }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  /**
   * Terminate all other sessions (keep current)
   */
  const terminateAllOtherSessions = async () => {
    if (!authStore.isAuthenticated) {
      throw new Error('Not authenticated')
    }

    try {
      const response = await fetch('/api/auth/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`,
          'X-CSRF-Token': await getCsrfToken()
        }
      })

      // Add defensive check for undefined response
      if (!response) {
        throw new Error('No response received from server')
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        // Keep only current session
        sessions.value = sessions.value.filter(session => session.isCurrent)
        return data
      } else {
        throw new Error(data.error?.message || 'Failed to terminate sessions')
      }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  /**
   * Refresh current session
   */
  const refreshCurrentSession = async () => {
    if (!authStore.isAuthenticated) {
      throw new Error('Not authenticated')
    }

    try {
      const response = await fetch('/api/auth/extend-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`,
          'X-CSRF-Token': await getCsrfToken()
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        // Refresh sessions list
        await fetchSessions()
        return data
      } else {
        throw new Error(data.error?.message || 'Failed to refresh session')
      }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  /**
   * Get CSRF token for API requests
   */
  const getCsrfToken = async () => {
    try {
      const response = await fetch('/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        return data.data?.csrfToken || ''
      }
    } catch (error) {
      console.warn('Failed to get CSRF token:', error)
    }

    return ''
  }

  /**
   * Start automatic session refresh
   */
  const startAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }

    refreshInterval.value = setInterval(() => {
      if (authStore.isAuthenticated) {
        fetchSessions()
      }
    }, REFRESH_INTERVAL)
  }

  /**
   * Stop automatic session refresh
   */
  const stopAutoRefresh = () => {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
  }

  /**
   * Check for new sessions (security monitoring)
   */
  const checkForNewSessions = () => {
    const previousSessionCount = sessions.value.length

    return fetchSessions().then(() => {
      const currentSessionCount = sessions.value.length

      if (currentSessionCount > previousSessionCount) {
        // New session detected
        return {
          newSessionDetected: true,
          newSessionCount: currentSessionCount - previousSessionCount
        }
      }

      return {
        newSessionDetected: false,
        newSessionCount: 0
      }
    })
  }

  /**
   * Get session statistics
   */
  const getSessionStats = computed(() => {
    return {
      total: totalSessions.value,
      active: activeSessions.value.length,
      current: currentSession.value ? 1 : 0,
      others: otherSessions.value.length,
      hasMultiple: hasMultipleSessions.value
    }
  })

  /**
   * Format session for display
   * @param {Object} session - Raw session object
   * @returns {Object} Formatted session object
   */
  const formatSession = (session) => {
    return {
      ...session,
      deviceType: getDeviceType(session.userAgent),
      browserName: getBrowserName(session.userAgent),
      location: session.ip || 'Unknown',
      formattedLastActivity: formatRelativeTime(session.lastActivity),
      formattedCreatedAt: formatRelativeTime(session.createdAt)
    }
  }

  /**
   * Get device type from user agent
   */
  const getDeviceType = (userAgent = '') => {
    const ua = userAgent.toLowerCase()

    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'mobile'
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'tablet'
    } else {
      return 'desktop'
    }
  }

  /**
   * Get browser name from user agent
   */
  const getBrowserName = (userAgent = '') => {
    const ua = userAgent.toLowerCase()

    // Check Edge first since it contains 'chrome' in its user agent
    if (ua.includes('edg/') || ua.includes('edge/')) return 'Edge'
    if (ua.includes('chrome')) return 'Chrome'
    if (ua.includes('firefox')) return 'Firefox'
    if (ua.includes('safari') && !ua.includes('chrome')) return 'Safari'
    if (ua.includes('opera')) return 'Opera'

    return 'Unknown'
  }

  /**
   * Format relative time
   */
  const formatRelativeTime = (date) => {
    const now = new Date()
    const past = new Date(date)
    const diffMs = now - past
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  // Lifecycle hooks
  onMounted(() => {
    if (authStore.isAuthenticated) {
      fetchSessions()
      startAutoRefresh()
    }
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  // Watch for authentication changes
  authStore.$subscribe((mutation, state) => {
    if (state.isAuthenticated && !refreshInterval.value) {
      fetchSessions()
      startAutoRefresh()
    } else if (!state.isAuthenticated && refreshInterval.value) {
      stopAutoRefresh()
      sessions.value = []
    }
  })

  return {
    // State
    sessions: computed(() => sessions.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // Computed
    activeSessions,
    currentSession,
    otherSessions,
    totalSessions,
    hasMultipleSessions,
    getSessionStats,

    // Methods
    fetchSessions,
    terminateSession,
    terminateAllOtherSessions,
    refreshCurrentSession,
    checkForNewSessions,
    formatSession,
    clearError,
    startAutoRefresh,
    stopAutoRefresh
  }
}
