/**
 * Authentication Store Unit Tests (TEST-001)
 * Tests for authentication store actions and mutations
 */

import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '../auth.js'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock the httpClient
vi.mock('../../utils/httpClient.js', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}))

// Import the mocked httpClient after mocking
import httpClient from '../../utils/httpClient.js'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('Authentication Store (TEST-001)', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    // Reset all mock functions
    Object.values(httpClient).forEach(mockFn => {
      if (typeof mockFn === 'function') {
        mockFn.mockReset()
      }
    })
  })

  describe('Initial State', () => {
    it('should initialize with correct defaults', () => {
      const store = useAuthStore()
      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(store.loading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.initialized).toBe(false)
    })
  })

  describe('Getters', () => {
    it('should return correct isLoggedIn status', () => {
      const store = useAuthStore()
      expect(store.isLoggedIn).toBe(false)

      store.isAuthenticated = true
      store.token = 'test-token'
      expect(store.isLoggedIn).toBe(true)
    })

    it('should return current user', () => {
      const store = useAuthStore()
      const testUser = { id: '1', email: '<EMAIL>' }
      store.user = testUser
      expect(store.currentUser).toEqual(testUser)
    })
  })

  describe('Login Action', () => {
    it('should login successfully', async () => {
      const store = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const mockResponse = {
        success: true,
        data: {
          user: { id: '1', email: '<EMAIL>' },
          token: 'test-token',
          refreshToken: 'refresh-token'
        }
      }

      httpClient.post.mockResolvedValue(mockResponse)

      const result = await store.login(credentials)

      expect(httpClient.post).toHaveBeenCalledWith('/auth/login', {
        email: credentials.email,
        password: credentials.password,
        rememberMe: false
      })
      expect(store.user).toEqual(mockResponse.data.user)
      expect(store.token).toBe(mockResponse.data.token)
      expect(store.isAuthenticated).toBe(true)
      expect(store.loading).toBe(false)
      expect(store.error).toBeNull()
      expect(result.success).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_refresh_token', 'refresh-token')
    })

    it('should handle login failure', async () => {
      const store = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'wrongpassword' }
      const mockError = {
        errorType: 'AUTHENTICATION_ERROR',
        message: 'Invalid credentials',
        statusCode: 401
      }

      httpClient.post.mockRejectedValue(mockError)

      const result = await store.login(credentials)

      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(store.error).toBe('Invalid email or password')
      expect(result.success).toBe(false)
    })

    it('should handle login exception', async () => {
      const store = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const mockError = {
        errorType: 'NETWORK_ERROR',
        message: 'Network error'
      }

      httpClient.post.mockRejectedValue(mockError)

      const result = await store.login(credentials)

      expect(store.error).toBe('Network error. Please check your connection.')
      expect(result.success).toBe(false)
      expect(store.loading).toBe(false)
    })
  })

  describe('Register Action', () => {
    it('should register successfully', async () => {
      const store = useAuthStore()
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }
      const mockResponse = {
        success: true,
        data: {
          user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        }
      }

      httpClient.post.mockResolvedValue(mockResponse)

      const result = await store.register(userData)

      expect(httpClient.post).toHaveBeenCalledWith('/auth/register', userData)
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockResponse.data.user)
      expect(result.message).toBe('Registration successful. Please check your email to verify your account.')
      expect(store.error).toBeNull()
    })

    it('should handle registration failure', async () => {
      const store = useAuthStore()
      const userData = { email: '<EMAIL>', password: '123' }
      const mockError = {
        errorType: 'VALIDATION_ERROR',
        message: 'Password too short',
        details: 'Password must be at least 8 characters'
      }

      httpClient.post.mockRejectedValue(mockError)

      const result = await store.register(userData)

      expect(store.error).toBe('Password must be at least 8 characters')
      expect(result.success).toBe(false)
    })
  })

  describe('Logout Action', () => {
    it('should logout successfully', async () => {
      const store = useAuthStore()
      // Set initial authenticated state
      store.user = { id: '1', email: '<EMAIL>' }
      store.token = 'test-token'
      store.isAuthenticated = true

      httpClient.post.mockResolvedValue({ success: true })

      await store.logout()

      expect(httpClient.post).toHaveBeenCalledWith('/auth/logout')
      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_refresh_token')
    })
  })

  describe('Token Refresh', () => {
    it('should refresh token successfully', async () => {
      const store = useAuthStore()
      localStorageMock.getItem.mockReturnValue('refresh-token')

      const mockRefreshResponse = {
        success: true,
        data: {
          token: 'new-token',
          refreshToken: 'new-refresh-token'
        }
      }

      const mockUserResponse = {
        success: true,
        data: {
          user: { id: '1', email: '<EMAIL>' }
        }
      }

      httpClient.post.mockResolvedValue(mockRefreshResponse)
      httpClient.get.mockResolvedValue(mockUserResponse)

      const result = await store.refreshToken()

      expect(httpClient.post).toHaveBeenCalledWith('/auth/refresh', {
        refreshToken: 'refresh-token'
      })
      expect(httpClient.get).toHaveBeenCalledWith('/auth/me')
      expect(store.token).toBe('new-token')
      expect(store.user).toEqual(mockUserResponse.data.user)
      expect(result.success).toBe(true)
    })

    it('should handle token refresh failure', async () => {
      const store = useAuthStore()
      localStorageMock.getItem.mockReturnValue(null) // No refresh token

      const result = await store.refreshToken()

      expect(result.success).toBe(false)
      expect(store.error).toBe('No refresh token available')
    })
  })

  describe('Password Reset', () => {
    it('should request password reset successfully', async () => {
      const store = useAuthStore()
      const email = '<EMAIL>'
      const mockResponse = { success: true }

      httpClient.post.mockResolvedValue(mockResponse)

      const result = await store.requestPasswordReset(email)

      expect(httpClient.post).toHaveBeenCalledWith('/auth/forgot-password', { email })
      expect(result.success).toBe(true)
      expect(result.message).toBe('Password reset email sent successfully')
    })

    it('should reset password successfully', async () => {
      const store = useAuthStore()
      const resetData = { token: 'reset-token', password: 'newpassword123' }
      const mockResponse = { success: true }

      httpClient.post.mockResolvedValue(mockResponse)

      const result = await store.resetPassword(resetData)

      expect(httpClient.post).toHaveBeenCalledWith('/auth/reset-password', {
        token: resetData.token,
        password: resetData.password,
        passwordConfirm: resetData.password
      })
      expect(result.success).toBe(true)
      expect(result.message).toBe('Password reset successfully')
    })
  })

  describe('State Persistence', () => {
    it('should persist auth state to localStorage', () => {
      const store = useAuthStore()
      store.user = { id: '1', email: '<EMAIL>' }
      store.token = 'test-token'

      store.persistAuthState()

      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'test-token')
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_user',
        JSON.stringify({ id: '1', email: '<EMAIL>' })
      )
    })

    it('should restore auth state from localStorage', () => {
      const store = useAuthStore()
      const mockUser = { id: '1', email: '<EMAIL>' }

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'stored-token'
        if (key === 'auth_user') return JSON.stringify(mockUser)
        return null
      })

      store.restoreAuthState()

      expect(store.token).toBe('stored-token')
      expect(store.user).toEqual(mockUser)
      expect(store.isAuthenticated).toBe(true)
    })

    it('should clear auth state', () => {
      const store = useAuthStore()
      store.user = { id: '1', email: '<EMAIL>' }
      store.token = 'test-token'
      store.isAuthenticated = true

      store.clearAuthState()

      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_refresh_token')
    })
  })

  describe('Initialization', () => {
    it('should initialize auth state', async () => {
      const store = useAuthStore()
      localStorageMock.getItem.mockReturnValue('stored-token')

      const mockUserResponse = {
        success: true,
        data: {
          user: { id: '1', email: '<EMAIL>' }
        }
      }

      httpClient.get.mockResolvedValue(mockUserResponse)

      await store.initializeAuth()

      expect(httpClient.get).toHaveBeenCalledWith('/auth/me')
      expect(store.initialized).toBe(true)
      expect(store.isAuthenticated).toBe(true)
      expect(store.user).toEqual(mockUserResponse.data.user)
      expect(store.token).toBe('stored-token')
    })
  })
})
