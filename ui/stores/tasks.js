/**
 * Tasks Store
 * Manages task state and database operations using Pinia
 */

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useHierarchicalTasks } from '../composables/useHierarchicalTasks.js'
import { useTaskFiltering } from '../composables/useTaskFiltering.js'
import httpClient from '../utils/httpClient.js'
import { traceEntry, traceExit, traceStoreAction, traceError } from '../utils/traceLogger.js'

import {
  checkTaskIdExists as checkTaskIdExistsUtil,
  generateTaskId as generateTaskIdUtil,
  validateLinkedTask as validateLinkedTaskUtil,
  validateTaskId as validateTaskIdUtil,
} from '../../common/validation/taskValidation.js'

export const useTasksStore = defineStore('tasks', () => {
  // State
  const tasks = ref([])
  const loading = ref(false)
  const error = ref(null)
  const uploadProgress = ref(0)
  const uploadStatus = ref('idle') // 'idle', 'uploading', 'processing', 'success', 'error'
  const lastUploadResult = ref(null)
  const fetchPromise = ref(null) // Track ongoing fetch promise

  // Composables
  const { searchTasks: searchTasksComposable, filterTasks: filterTasksComposable } = useTaskFiltering()
  const { getHierarchicalTasks: getHierarchicalTasksComposable } = useHierarchicalTasks()

  // Computed properties
  const tasksByType = computed(() => {
    const types = {}
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    for (const task of tasksArray) {
      if (!types[task.type]) {
        types[task.type] = []
      }
      types[task.type].push(task)
    }
    return types
  })

  const tasksByPriority = computed(() => {
    const priorities = {}
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    for (const task of tasksArray) {
      if (!priorities[task.priority]) {
        priorities[task.priority] = []
      }
      priorities[task.priority].push(task)
    }
    return priorities
  })

  const tasksByStatus = computed(() => {
    const statuses = {}
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    for (const task of tasksArray) {
      if (!statuses[task.status]) {
        statuses[task.status] = []
      }
      statuses[task.status].push(task)
    }
    return statuses
  })

  const epics = computed(() => {
    const uniqueEpics = new Set()
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    for (const task of tasksArray) {
      if (task.epic) {
        uniqueEpics.add(task.epic)
      }
    }
    return Array.from(uniqueEpics).sort()
  })

  const statistics = computed(() => {
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    return {
      total: tasksArray.length,
      byType: Object.keys(tasksByType.value).map(type => ({
        type,
        count: tasksByType.value[type].length,
      })),
      byPriority: Object.keys(tasksByPriority.value).map(priority => ({
        priority,
        count: tasksByPriority.value[priority].length,
      })),
      byStatus: Object.keys(tasksByStatus.value).map(status => ({
        status,
        count: tasksByStatus.value[status].length,
      })),
    }
  })

  const tasksByProject = computed(() => {
    const projectGroups = {}
    tasks.value.forEach(task => {
      const projectId = task.project_id
      if (!projectGroups[projectId]) {
        projectGroups[projectId] = []
      }
      projectGroups[projectId].push(task)
    })
    return projectGroups
  })

  const getProjectStatistics = computed(() => {
    return (projectId) => {
      const projectTasks = tasks.value.filter(task => task.project_id === projectId)
      const totalTasks = projectTasks.length
      const completedTasks = projectTasks.filter(task => task.status === 'Done').length
      const inProgressTasks = projectTasks.filter(task => task.status === 'In Progress').length
      const backlogTasks = projectTasks.filter(task => task.status === 'Backlog').length

      return {
        totalTasks,
        completedTasks,
        inProgressTasks,
        backlogTasks,
        completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
      }
    }
  })

  // Actions
  const fetchTasks = async (filters = {}) => {
    const startTime = Date.now()

    // Trace store action entry
    traceStoreAction('-> ', 'TasksStore', 'fetchTasks', filters)
    traceEntry('-> ', 'TasksStore.fetchTasks', filters)

    // If there's already a fetch in progress, return that promise
    if (fetchPromise.value) {
      traceExit('-> ', 'TasksStore.fetchTasks', { cached: true }, Date.now() - startTime)
      return fetchPromise.value
    }

    loading.value = true
    error.value = null

    // Create and store the fetch promise
    fetchPromise.value = (async () => {
      try {
        const response = await httpClient.get('/tasks', { params: filters })
        // Handle different response structures - response might be the data directly or wrapped in .data
        const responseData = response.data || response

        // The API now returns {tasks: Array, metadata: Object} structure
        // Extract the tasks array from the response
        let fetchedTasks
        if (responseData.tasks && Array.isArray(responseData.tasks.tasks)) {
          // New API format: {tasks: [...], metadata: {...}}
          fetchedTasks = responseData.tasks.tasks
        } else if (Array.isArray(responseData)) {
          // Legacy format: direct array
          fetchedTasks = responseData
        } else {
          // Fallback for unexpected formats
          console.warn('fetchTasks: Unexpected response format:', typeof responseData, responseData)
          fetchedTasks = []
        }

        // Ensure fetchedTasks is always an array
        if (!Array.isArray(fetchedTasks)) {
          console.warn('fetchTasks: Expected array but got:', typeof fetchedTasks, fetchedTasks)
          tasks.value = []
        } else {
          tasks.value = fetchedTasks
        }

        const duration = Date.now() - startTime
        traceExit('-> ', 'TasksStore.fetchTasks', { count: fetchedTasks.length, success: true }, duration)
        return fetchedTasks
      } catch (error_) {
        error.value = `Failed to fetch tasks: ${error_.message}`
        const duration = Date.now() - startTime
        traceError('-> ', 'TasksStore.fetchTasks', error_, { duration_ms: duration })
        console.error('Error fetching tasks:', error_)
        throw error_
      } finally {
        loading.value = false
        fetchPromise.value = null // Clear the promise when done
      }
    })()

    return fetchPromise.value
  }

  const uploadTasksFromJson = async (jsonData, projectId) => {
    uploadStatus.value = 'processing'
    uploadProgress.value = 0
    error.value = null
    lastUploadResult.value = null

    try {
      // Validate JSON structure
      if (!Array.isArray(jsonData)) {
        throw new TypeError('JSON data must be an array of tasks')
      }

      if (jsonData.length === 0) {
        throw new Error('No tasks found in the uploaded file')
      }

      if (!projectId) {
        throw new Error('Project ID is required for uploading tasks')
      }

      // Simulate progress for user feedback
      uploadProgress.value = 25

      // Insert tasks into database with project ID
      const response = await httpClient.post('/tasks/bulk', {
        tasks: jsonData,
        project_id: projectId
      })
      const result = response.data || response

      uploadProgress.value = 75

      // Refresh tasks from database
      await fetchTasks()

      uploadProgress.value = 100
      uploadStatus.value = 'success'
      lastUploadResult.value = result

      return result
    } catch (error_) {
      error.value = `Failed to upload tasks: ${error_.message}`
      uploadStatus.value = 'error'
      console.error('Error uploading tasks:', error_)
      throw error_
    }
  }

  const logTaskStatusDistribution = () => {
    const statusCounts = {}
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    for (const task of tasksArray) {
      statusCounts[task.status] = (statusCounts[task.status] || 0) + 1
    }
    console.log('Current task status distribution:', statusCounts)
  }

  const updateTaskStatus = async (originalTaskId, newStatus) => {
    try {
      // Find the task in local state to get its PocketBase 'id'
      // Ensure tasks.value is always an array
      const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
      const taskToUpdate = tasksArray.find(task => task.task_id === originalTaskId)

      if (!taskToUpdate) {
        console.error(`Task with original ID ${originalTaskId} not found.`)
        return false
      }

      console.log(`Updating task ${originalTaskId} from "${taskToUpdate.status}" to "${newStatus}"`)

      // Use the task_id for the API call
      const response = await httpClient.patch(`/tasks/${originalTaskId}/status`, {
        status: newStatus
      })
      const success = response.success

      if (success) {
        // Update local state using the originalTaskId
        // Ensure tasks.value is always an array
        if (Array.isArray(tasks.value)) {
          const taskIndex = tasks.value.findIndex(task => task.task_id === originalTaskId)
          if (taskIndex !== -1) {
            // Create a new object to ensure reactivity
            const updatedTask = { ...tasks.value[taskIndex], status: newStatus }
            // Replace the task in the array to trigger reactivity
            tasks.value.splice(taskIndex, 1, updatedTask)
          }
        }

        // Log status distribution after update
        logTaskStatusDistribution()
      }

      return success
    } catch (error_) {
      error.value = `Failed to update task status: ${error_.message}`
      console.error('Error updating task status:', error_)
      throw error_
    }
  }

  const updateTask = async (taskId, taskData) => {
    try {
      // Find the task in local state to get its PocketBase 'id'
      // Ensure tasks.value is always an array
      const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
      const taskToUpdate = tasksArray.find(task => task.task_id === taskId || task.id === taskId)

      if (!taskToUpdate) {
        console.error(`Task with ID ${taskId} not found.`)
        return false
      }

      // Use the task_id for the API call
      const response = await httpClient.put(`/tasks/${taskId}`, taskData)
      const responseData = response.data || response
      const updatedTask = responseData.task

      if (updatedTask) {
        // Update local state
        // Ensure tasks.value is always an array
        if (Array.isArray(tasks.value)) {
          const taskIndex = tasks.value.findIndex(task => task.id === taskToUpdate.id)
          if (taskIndex !== -1) {
            tasks.value[taskIndex] = { ...tasks.value[taskIndex], ...updatedTask }
          }
        }
        return updatedTask
      }

      return false
    } catch (error_) {
      error.value = `Failed to update task: ${error_.message}`
      console.error('Error updating task:', error_)
      throw error_
    }
  }

  const addTask = async taskData => {
    try {
      // Validate required project_id
      if (!taskData.project_id) {
        throw new Error('Project ID is required for creating tasks')
      }

      // Use provided task_id or generate one using the utility function
      const taskId = taskData.task_id || generateTaskIdUtil(tasks.value)

      // Validate task ID format and uniqueness using the utility function
      const validationError = validateTaskIdUtil(taskId, tasks.value)
      if (validationError) {
        throw new Error(validationError)
      }

      // Prepare task data with defaults
      const newTaskData = {
        task_id: taskId,
        parent_id: null,
        summary: taskData.summary,
        description: taskData.description || null,
        linked_tasks: taskData.linked_tasks || [],
        epic: taskData.epic || null,
        priority: taskData.priority || 'Medium',
        estimated_effort: taskData.estimated_effort || 'Medium',
        type: taskData.type || 'Task',
        status: taskData.status || 'Backlog',
        assigned_to: taskData.assigned_to || null,
        project_id: taskData.project_id, // Required project reference
      }

      // Create task in database
      const response = await httpClient.post('/tasks', newTaskData)
      const responseData = response.data || response
      const createdTask = responseData.task

      if (createdTask) {
        // Add to local state
        tasks.value.unshift(createdTask)
        return createdTask
      }

      return false
    } catch (error_) {
      error.value = `Failed to add task: ${error_.message}`
      console.error('Error adding task:', error_)
      throw error_
    }
  }

  const deleteTask = async taskId => {
    try {
      // Find the task in local state to get its PocketBase 'id'
      const taskToDelete = tasks.value.find(task => task.task_id === taskId || task.id === taskId)

      if (!taskToDelete) {
        console.error(`Task with ID ${taskId} not found.`)
        return false
      }

      // Use the task_id for the API call
      const response = await httpClient.delete(`/tasks/${taskId}`)
      const success = response.success

      if (success) {
        // Remove from local state
        const taskIndex = tasks.value.findIndex(task => task.id === taskToDelete.id)
        if (taskIndex !== -1) {
          tasks.value.splice(taskIndex, 1)
        }
        return true
      }

      return false
    } catch (error_) {
      error.value = `Failed to delete task: ${error_.message}`
      console.error('Error deleting task:', error_)
      throw error_
    }
  }

  const getTaskById = async taskId => {
    try {
      // Get task by task_id
      const response = await httpClient.get(`/tasks/${taskId}`)
      const responseData = response.data || response
      return responseData.task
    } catch (error_) {
      error.value = `Failed to fetch task: ${error_.message}`
      console.error('Error fetching task:', error_)
      throw error_
    }
  }

  const clearAllTasks = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await httpClient.delete('/tasks/all', { confirm: true })
      const responseData = response.data || response
      const deletedCount = responseData.deleted_count
      tasks.value = []
      return deletedCount
    } catch (error_) {
      error.value = `Failed to clear tasks: ${error_.message}`
      console.error('Error clearing tasks:', error_)
      throw error_
    } finally {
      loading.value = false
    }
  }

  const getTasksByProject = async (projectId, filters = {}) => {
    try {
      const response = await httpClient.get(`/projects/${projectId}/tasks`, { params: filters })
      const responseData = response.data || response

      // The project tasks endpoint returns {tasks: Array} structure (no metadata)
      // Extract the tasks array from the response
      let projectTasks
      if (responseData.tasks && Array.isArray(responseData.tasks)) {
        // API format: {tasks: [...]}
        projectTasks = responseData.tasks
      } else if (Array.isArray(responseData)) {
        // Legacy format: direct array
        projectTasks = responseData
      } else {
        // Fallback for unexpected formats
        console.warn('getTasksByProject: Unexpected response format:', typeof responseData, responseData)
        projectTasks = []
      }

      return projectTasks
    } catch (error_) {
      error.value = `Failed to fetch project tasks: ${error_.message}`
      console.error('Error fetching project tasks:', error_)
      throw error_
    }
  }

  const filterTasksByProject = (projectId) => {
    // Ensure tasks.value is always an array
    const tasksArray = Array.isArray(tasks.value) ? tasks.value : []
    return tasksArray.filter(task => task.project_id === projectId)
  }

  const resetUploadState = () => {
    uploadStatus.value = 'idle'
    uploadProgress.value = 0
    lastUploadResult.value = null
    error.value = null
  }

  // Use composables for search, filter, and hierarchical tasks
  const searchTasks = (tasksToSearch, query) => {
    return searchTasksComposable(tasksToSearch, query)
  }

  const filterTasks = (tasksToFilter, filters) => {
    return filterTasksComposable(tasksToFilter, filters)
  }

  const getHierarchicalTasks = allTasks => {
    return getHierarchicalTasksComposable(allTasks)
  }

  // Use utility functions for validation and ID generation, passing tasks.value where needed
  const validateTaskId = taskId => {
    return validateTaskIdUtil(taskId, tasks.value)
  }

  const checkTaskIdExists = taskId => {
    return checkTaskIdExistsUtil(taskId, tasks.value)
  }

  const validateLinkedTask = taskId => {
    return validateLinkedTaskUtil(taskId, tasks.value)
  }

  const generateTaskId = () => {
    return generateTaskIdUtil(tasks.value)
  }

  return {
    // State
    tasks,
    loading,
    error,
    uploadProgress,
    uploadStatus,
    lastUploadResult,

    // Computed
    tasksByType,
    tasksByPriority,
    tasksByStatus,
    tasksByProject,
    epics,
    statistics,
    getProjectStatistics,

    // Actions
    fetchTasks,
    uploadTasksFromJson,
    updateTaskStatus,
    updateTask,
    addTask,
    deleteTask,
    getTaskById,
    clearAllTasks,
    getTasksByProject,
    filterTasksByProject,
    resetUploadState,
    searchTasks,
    filterTasks,
    getHierarchicalTasks,
    logTaskStatusDistribution,

    // Validation methods
    validateTaskId,
    checkTaskIdExists,
    validateLinkedTask,
    generateTaskId,
  }
})
