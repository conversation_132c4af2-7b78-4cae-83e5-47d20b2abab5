import { defineStore } from 'pinia'
import httpClient from '../utils/httpClient.js'
import { traceEntry, traceExit, traceStoreAction, traceError } from '../utils/traceLogger.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
    error: null,
    initialized: false,
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && !!state.token,
    currentUser: (state) => state.user,
    authToken: (state) => state.token,
    isLoading: (state) => state.loading,
    authError: (state) => state.error,
    isInitialized: (state) => state.initialized,
  },

  actions: {
    async login(credentials) {
      const startTime = Date.now()

      // Trace store action entry
      traceStoreAction('-> ', 'AuthStore', 'login', { email: credentials?.email, rememberMe: credentials?.rememberMe })
      traceEntry('-> ', 'AuthStore.login', { email: credentials?.email, rememberMe: credentials?.rememberMe })

      this.loading = true
      this.error = null

      try {
        // Validate credentials
        if (!credentials || !credentials.email || !credentials.password) {
          this.error = 'Email and password are required'
          const duration = Date.now() - startTime
          traceExit('-> ', 'AuthStore.login', { success: false, error: this.error }, duration)
          return { success: false, error: this.error }
        }

        // Trim and validate non-empty
        const email = credentials.email.trim()
        const password = credentials.password.trim()

        if (!email || !password) {
          this.error = 'Email and password cannot be empty'
          const duration = Date.now() - startTime
          traceExit('-> ', 'AuthStore.login', { success: false, error: this.error }, duration)
          return { success: false, error: this.error }
        }

        const response = await httpClient.post('/auth/login', {
          email,
          password,
          rememberMe: credentials.rememberMe || false
        })

        const responseData = response.data || response
        if (response.success && responseData) {
          this.user = responseData.user
          this.token = responseData.token
          this.isAuthenticated = true

          // Store refresh token if provided
          if (responseData.refreshToken) {
            localStorage.setItem('auth_refresh_token', responseData.refreshToken)
          }

          // Persist to localStorage
          this.persistAuthState()

          return { success: true, user: responseData.user }
        } else {
          this.error = response.error?.message || 'Login failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'AUTHENTICATION_ERROR') {
          this.error = 'Invalid email or password'
        } else if (error.errorType === 'VALIDATION_ERROR') {
          this.error = error.details || error.message || 'Invalid input'
        } else if (error.errorType === 'RATE_LIMIT_ERROR') {
          this.error = 'Too many login attempts. Please try again later.'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Login failed'
        }

        // Clear auth state but preserve error
        this.user = null
        this.token = null
        this.isAuthenticated = false
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async logout() {
      this.loading = true
      this.error = null

      try {
        await httpClient.post('/auth/logout')

        this.clearAuthState()
        this.clearPersistedState()

        return { success: true }
      } catch (error) {
        this.error = error.message || 'Logout failed'
        // Clear local state even if API call fails
        this.clearAuthState()
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async refreshToken() {
      this.loading = true
      this.error = null

      try {
        // Get refresh token from localStorage
        const refreshToken = localStorage.getItem('auth_refresh_token')
        if (!refreshToken) {
          throw new Error('No refresh token available')
        }

        const response = await httpClient.post('/auth/refresh', {
          refreshToken
        })

        const responseData = response.data || response
        if (response.success && responseData && responseData.token) {
          this.token = responseData.token

          // Update refresh token if provided
          if (responseData.refreshToken) {
            localStorage.setItem('auth_refresh_token', responseData.refreshToken)
          }

          // Get updated user info
          const userResponse = await httpClient.get('/auth/me')
          const userResponseData = userResponse.data || userResponse
          if (userResponse.success && userResponseData) {
            this.user = userResponseData.user
            this.isAuthenticated = true
            this.persistAuthState()
            return { success: true, token: this.token }
          } else {
            throw new Error('Failed to get user info after token refresh')
          }
        } else {
          throw new Error('Invalid refresh response')
        }
      } catch (error) {
        this.error = error.message || 'Token refresh failed'
        // Clear auth state but preserve error
        this.user = null
        this.token = null
        this.isAuthenticated = false
        this.clearPersistedState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async initializeAuth() {
      this.loading = true
      this.error = null

      try {
        // Check if we have a token in localStorage
        const token = localStorage.getItem('auth_token')

        if (token) {
          // Try to get current user with the token
          try {
            const userResponse = await httpClient.get('/auth/me')

            const userResponseData = userResponse.data || userResponse
            if (userResponse.success && userResponseData) {
              this.user = userResponseData.user
              this.token = token
              this.isAuthenticated = true
              this.persistAuthState()
              return { success: true, user: this.user }
            } else {
              // Invalid response format, clear auth state
              this.clearAuthState()
              return { success: false, error: 'Invalid authentication response' }
            }
          } catch (error) {
            // Handle different types of errors
            if (error.statusCode === 401) {
              // Token expired or invalid, try to refresh
              const refreshResult = await this.refreshToken()
              if (refreshResult.success) {
                return { success: true, user: this.user }
              }
              // Refresh failed, clear auth state
              this.clearAuthState()
              return { success: false, error: 'Session expired' }
            } else if (error.statusCode === 0 || error.type === 'NETWORK_ERROR') {
              // Network error, keep existing auth state but mark as uninitialized
              // This allows the app to work offline with cached auth state
              console.warn('Network error during auth initialization, keeping cached state')
              const cachedState = this.getPersistedState()
              if (cachedState.token && cachedState.user) {
                this.user = cachedState.user
                this.token = cachedState.token
                this.isAuthenticated = true
                return { success: true, user: this.user, offline: true }
              }
            }

            // Other errors, clear auth state
            this.clearAuthState()
            return { success: false, error: error.message || 'Authentication validation failed' }
          }
        }

        // No token found, clear auth state
        this.clearAuthState()
        return { success: false, error: 'No authentication token found' }
      } catch (error) {
        this.error = error.message || 'Auth initialization failed'
        this.clearAuthState()
        return { success: false, error: this.error }
      } finally {
        this.loading = false
        this.initialized = true
      }
    },

    async register(userData) {
      this.loading = true
      this.error = null

      try {
        const response = await httpClient.post('/auth/register', userData)

        const responseData = response.data || response
        if (response.success && responseData) {
          // Registration successful, but user needs to verify email
          // Don't automatically authenticate until email is verified
          return {
            success: true,
            user: responseData.user,
            message: 'Registration successful. Please check your email to verify your account.'
          }
        } else {
          this.error = response.error?.message || 'Registration failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'VALIDATION_ERROR') {
          this.error = error.details || error.message || 'Invalid registration data'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Registration failed'
        }
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async updateProfile(userData) {
      this.loading = true
      this.error = null

      try {
        const response = await httpClient.put('/auth/profile', userData)

        const responseData = response.data || response
        if (response.success && responseData) {
          this.user = { ...this.user, ...responseData.user }
          this.persistAuthState()
          return { success: true, user: this.user }
        } else {
          this.error = response.error?.message || 'Profile update failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'VALIDATION_ERROR') {
          this.error = error.details || error.message || 'Invalid profile data'
        } else if (error.errorType === 'AUTHENTICATION_ERROR') {
          this.error = 'Authentication required. Please log in again.'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Profile update failed'
        }
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async requestPasswordReset(email) {
      this.loading = true
      this.error = null

      try {
        const response = await httpClient.post('/auth/forgot-password', { email })

        if (response.success) {
          return {
            success: true,
            message: 'Password reset email sent successfully'
          }
        } else {
          this.error = response.error?.message || 'Password reset request failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'VALIDATION_ERROR') {
          this.error = 'Please provide a valid email address'
        } else if (error.errorType === 'RATE_LIMIT_ERROR') {
          this.error = 'Too many reset requests. Please try again later.'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Password reset request failed'
        }
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async confirmPasswordReset(token, password) {
      this.loading = true
      this.error = null

      try {
        const response = await httpClient.post('/auth/reset-password', {
          token,
          password,
          passwordConfirm: password
        })

        if (response.success) {
          return {
            success: true,
            message: 'Password reset successfully'
          }
        } else {
          this.error = response.error?.message || 'Password reset confirmation failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'VALIDATION_ERROR') {
          this.error = error.details || 'Invalid reset token or password'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Password reset confirmation failed'
        }
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // Reset password method that calls the API directly
    async resetPassword(tokenOrData, password) {
      this.loading = true
      this.error = null

      try {
        let requestData
        // Handle both object format and separate parameters
        if (typeof tokenOrData === 'object') {
          requestData = {
            token: tokenOrData.token,
            password: tokenOrData.password,
            passwordConfirm: tokenOrData.password
          }
        } else {
          requestData = {
            token: tokenOrData,
            password,
            passwordConfirm: password
          }
        }

        const response = await httpClient.post('/auth/reset-password', requestData)

        if (response.success) {
          return {
            success: true,
            message: 'Password reset successfully'
          }
        } else {
          this.error = response.error?.message || 'Password reset failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'VALIDATION_ERROR') {
          this.error = error.details || 'Invalid reset token or password'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Password reset failed'
        }
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    async changePassword(currentPasswordOrData, newPassword) {
      this.loading = true
      this.error = null

      try {
        let requestData
        // Handle both object format and separate parameters
        if (typeof currentPasswordOrData === 'object') {
          requestData = {
            currentPassword: currentPasswordOrData.currentPassword,
            newPassword: currentPasswordOrData.newPassword,
            passwordConfirm: currentPasswordOrData.newPassword
          }
        } else {
          requestData = {
            currentPassword: currentPasswordOrData,
            newPassword,
            passwordConfirm: newPassword
          }
        }

        const response = await httpClient.put('/auth/password', requestData)

        if (response.success) {
          return {
            success: true,
            message: 'Password changed successfully'
          }
        } else {
          this.error = response.error?.message || 'Password change failed'
          return { success: false, error: this.error }
        }
      } catch (error) {
        // Handle HTTP errors with specific messages
        if (error.errorType === 'VALIDATION_ERROR') {
          this.error = error.details || 'Invalid password data'
        } else if (error.errorType === 'AUTHENTICATION_ERROR') {
          this.error = 'Current password is incorrect'
        } else if (error.errorType === 'NETWORK_ERROR') {
          this.error = 'Network error. Please check your connection.'
        } else {
          this.error = error.message || 'Password change failed'
        }
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    restoreAuthState() {
      const persistedState = this.getPersistedState()

      // Handle partial data gracefully
      if (persistedState.token) {
        this.token = persistedState.token

        // Only set authenticated if we have both token and user
        if (persistedState.user) {
          this.user = persistedState.user
          this.isAuthenticated = true
        } else {
          // Token without user - keep token but don't authenticate
          this.user = null
          this.isAuthenticated = false
        }
      } else {
        // No token - clear everything
        this.token = null
        this.user = null
        this.isAuthenticated = false
      }
    },

    clearError() {
      this.error = null
    },

    clearAuthState() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.clearPersistedState()
    },

    /**
     * Handle token expiration by clearing auth state and redirecting to login
     * @param {string} redirectPath - Path to redirect to after login
     */
    handleTokenExpiration(redirectPath = null) {
      console.warn('Token expired, logging out user')
      this.clearAuthState()

      // If we're in a browser environment, redirect to login
      if (typeof window !== 'undefined' && window.location) {
        const currentPath = redirectPath || window.location.pathname + window.location.search
        const loginUrl = `/login${currentPath !== '/login' ? `?redirect=${encodeURIComponent(currentPath)}` : ''}`
        window.location.href = loginUrl
      }
    },

    /**
     * Validate current authentication state
     * @returns {Promise<boolean>} True if authentication is valid
     */
    async validateAuthState() {
      if (!this.isAuthenticated || !this.token) {
        return false
      }

      try {
        const response = await httpClient.get('/auth/me')
        const responseData = response.data || response
        return response.success && responseData
      } catch (error) {
        if (error.statusCode === 401) {
          this.handleTokenExpiration()
          return false
        }
        // For other errors, assume auth is still valid to handle network issues
        return true
      }
    },

    persistAuthState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          localStorage.setItem('auth_user', JSON.stringify(this.user))
          localStorage.setItem('auth_token', this.token)
        } catch (error) {
          // Handle localStorage errors gracefully (quota exceeded, disabled, etc.)
          console.warn('Failed to persist auth state:', error.message)
          // Don't throw - this is not critical for functionality
        }
      }
    },

    getPersistedState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          const user = localStorage.getItem('auth_user')
          const token = localStorage.getItem('auth_token')

          return {
            user: user ? JSON.parse(user) : null,
            token: token || null,
          }
        } catch (error) {
          // Handle localStorage errors gracefully (corrupted data, disabled, etc.)
          console.warn('Failed to get persisted auth state:', error.message)
          return { user: null, token: null }
        }
      }

      return { user: null, token: null }
    },

    clearPersistedState() {
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          localStorage.removeItem('auth_user')
          localStorage.removeItem('auth_token')
          localStorage.removeItem('auth_refresh_token')
        } catch (error) {
          // Handle localStorage errors gracefully
          console.warn('Failed to clear persisted auth state:', error.message)
          // Don't throw - this is not critical for functionality
        }
      }
    },

    // Helper methods for token management (replacing PocketBase methods)

    /**
     * Get current authentication token
     * @returns {string|null} Authentication token
     */
    getToken() {
      return this.token || localStorage.getItem('auth_token')
    },

    /**
     * Check if user is authenticated (local validation)
     * @returns {boolean} True if user appears to be authenticated
     */
    isAuthenticatedLocally() {
      const token = this.getToken()
      return !!(token && this.user && this.isAuthenticated)
    },

    /**
     * Get current user data
     * @returns {Object|null} User data or null if not authenticated
     */
    getCurrentUserData() {
      return this.user
    },

    /**
     * Validate token format (basic client-side validation)
     * @param {string} token - Token to validate
     * @returns {boolean} True if token format appears valid
     */
    isValidTokenFormat(token) {
      // Basic validation - check if token exists and has reasonable length
      return !!(token && typeof token === 'string' && token.length > 10)
    },
  },
})
