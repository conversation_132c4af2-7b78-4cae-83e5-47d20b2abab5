<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      app
      temporary
    >
      <v-list>
        <v-list-item
          prepend-avatar="@/assets/logo.png"
          title="Track Tasks"
          subtitle="Project Task Management"
        />
      </v-list>

      <v-divider />

      <v-list density="compact" nav>
        <v-list-item
          v-for="item in navigationItems"
          :key="item.title"
          :prepend-icon="item.icon"
          :title="item.title"
          :subtitle="item.subtitle"
          :to="item.to"
          :value="item.value"
          class="mb-1"
        >
          <template #append v-if="item.badge">
            <v-badge
              :content="item.badge"
              color="primary"
              inline
            />
          </template>
        </v-list-item>
      </v-list>

      <template #append>
        <div class="pa-4">
          <v-btn
            block
            variant="outlined"
            prepend-icon="mdi-github"
            href="https://github.com"
            target="_blank"
          >
            GitHub
          </v-btn>
        </div>
      </template>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar elevation="2" color="primary" density="comfortable">
      <v-app-bar-nav-icon @click="drawer = !drawer" />

      <v-app-bar-title class="d-flex align-center">
        <v-icon class="me-3">mdi-format-list-checks</v-icon>
        Track Tasks
      </v-app-bar-title>

      <v-spacer />

      <!-- Desktop Navigation -->
      <div class="d-none d-md-flex">
        <v-btn
          v-for="item in navigationItems"
          :key="item.title"
          :to="item.to"
          :variant="$route.path === item.to ? 'elevated' : 'text'"
          :prepend-icon="item.icon"
          class="me-2"
        >
          {{ item.title }}
          <v-badge
            v-if="item.badge"
            :content="item.badge"
            color="white"
            text-color="primary"
            floating
          />
        </v-btn>
      </div>

      <!-- Task Count Badge -->
      <v-chip
        v-if="tasksStore.tasks.length > 0"
        color="white"
        text-color="primary"
        variant="elevated"
        size="small"
        class="ms-2"
      >
        {{ tasksStore.tasks.length }} Tasks
      </v-chip>

      <!-- Authentication Section -->
      <div v-if="authStore.isAuthenticated" class="d-flex align-center ms-3">
        <!-- User Avatar Menu -->
        <v-menu v-model="userMenuOpen" :close-on-content-click="false">
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              icon
              variant="text"
              size="small"
            >
              <v-avatar size="32">
                <v-img
                  v-if="authStore.user?.avatar"
                  :src="authStore.user.avatar"
                  :alt="authStore.user.name || authStore.user.email"
                />
                <v-icon v-else>mdi-account-circle</v-icon>
              </v-avatar>
            </v-btn>
          </template>

          <v-card min-width="250">
            <v-card-text>
              <div class="d-flex align-center mb-3">
                <v-avatar size="40" class="me-3">
                  <v-img
                    v-if="authStore.user?.avatar"
                    :src="authStore.user.avatar"
                    :alt="authStore.user.name || authStore.user.email"
                  />
                  <v-icon v-else>mdi-account-circle</v-icon>
                </v-avatar>
                <div>
                  <div class="font-weight-medium">{{ authStore.user?.name || 'User' }}</div>
                  <div class="text-caption text-medium-emphasis">{{ authStore.user?.email }}</div>
                </div>
              </div>

              <v-divider class="mb-3" />

              <v-list density="compact">
                <v-list-item
                  prepend-icon="mdi-account-cog"
                  title="Profile Settings"
                  @click="navigateToProfile"
                />
                <v-list-item
                  prepend-icon="mdi-logout"
                  title="Sign Out"
                  @click="handleLogout"
                />
              </v-list>
            </v-card-text>
          </v-card>
        </v-menu>
      </div>

      <!-- Login Button for Non-Authenticated Users -->
      <div v-else class="ms-3">
        <v-btn
          color="white"
          variant="outlined"
          prepend-icon="mdi-login"
          @click="navigateToLogin"
        >
          Sign In
        </v-btn>
      </div>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <router-view />
    </v-main>

    <AppFooter />
  </v-app>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTasksStore } from '@/stores/tasks'
import { useAuthStore } from '@/stores/auth'
import AppFooter from '@/components/AppFooter.vue'

const route = useRoute()
const router = useRouter()
const tasksStore = useTasksStore()
const authStore = useAuthStore()

const drawer = ref(false)
const userMenuOpen = ref(false)

const navigationItems = computed(() => [
  {
    title: 'Tasks',
    subtitle: 'View and manage tasks',
    icon: 'mdi-format-list-checks',
    to: '/',
    value: 'tasks',
    badge: tasksStore.tasks.length > 0 ? tasksStore.tasks.length : null
  },
  {
    title: 'Upload',
    subtitle: 'Import task data',
    icon: 'mdi-file-upload',
    to: '/upload',
    value: 'upload'
  },
  {
    title: 'Help',
    subtitle: 'Documentation & guides',
    icon: 'mdi-help-circle',
    to: '/help',
    value: 'help'
  }
])

// Methods
const navigateToLogin = () => {
  router.push('/login')
}

const navigateToProfile = () => {
  router.push('/profile')
  userMenuOpen.value = false
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    userMenuOpen.value = false
    // Optionally redirect to login page
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

onMounted(async () => {
  // Initialize auth store first
  await authStore.initializeAuth()

  // Then fetch tasks if user is authenticated
  if (authStore.isAuthenticated) {
    try {
      await tasksStore.fetchTasks()
    } catch (error) {
      console.error('Failed to fetch tasks in layout:', error)
      // Don't throw the error to prevent unhandled promise rejection
    }
  }
})
</script>
