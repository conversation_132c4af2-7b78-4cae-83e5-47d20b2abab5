<template>
  <v-container class="pa-6" fluid>
    <!-- Loading State -->
    <div v-if="loading" class="d-flex justify-center align-center" style="min-height: 400px;">
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </div>

    <!-- Error State -->
    <v-alert
      v-else-if="error"
      class="mb-4"
      type="error"
      variant="tonal"
    >
      {{ error }}
    </v-alert>

    <!-- Task Not Found -->
    <v-card v-else-if="!task" class="text-center pa-8" elevation="2">
      <v-icon class="mb-4" color="grey-lighten-2" size="64">
        mdi-file-question-outline
      </v-icon>
      <h2 class="text-h5 mb-4">Task Not Found</h2>
      <p class="text-body-1 text-medium-emphasis mb-6">
        The requested task could not be found in the database.
      </p>
      <v-btn color="primary" @click="$router.push('/')">
        <v-icon start>mdi-arrow-left</v-icon>
        Back to Tasks
      </v-btn>
    </v-card>

    <!-- Task Details -->
    <div v-else>
      <!-- Header with Navigation -->
      <v-row class="mb-4">
        <v-col>
          <div class="d-flex align-center justify-space-between">
            <v-btn
              class="text-none"
              prepend-icon="mdi-arrow-left"
              variant="text"
              @click="$router.push('/')"
            >
              {{ isNewTask ? 'Back to Tasks' : 'Back to Tasks' }}
            </v-btn>

            <div v-if="task" class="d-flex align-center ga-2">
              <v-chip
                :color="getPriorityColor(task.priority)"
                size="small"
                variant="elevated"
              >
                {{ task.priority }} Priority
              </v-chip>
              <v-chip
                :color="getStatusColor(task.status)"
                size="small"
                variant="tonal"
              >
                {{ task.status }}
              </v-chip>

              <!-- Edit Mode Toggle Button -->
              <v-btn
                v-if="!isEditMode && !isNewTask"
                color="primary"
                prepend-icon="mdi-pencil"
                size="small"
                variant="outlined"
                @click="enterEditMode"
              >
                Edit
              </v-btn>

              <!-- Delete Button -->
              <v-btn
                v-if="!isEditMode && !isNewTask"
                color="error"
                prepend-icon="mdi-delete"
                size="small"
                variant="outlined"
                @click="openDeleteDialog"
              >
                Delete Task
              </v-btn>

              <!-- Save/Cancel Buttons in Edit Mode -->
              <div v-else class="d-flex ga-2">
                <v-btn
                  color="success"
                  :loading="editLoading"
                  prepend-icon="mdi-check"
                  size="small"
                  variant="elevated"
                  @click="saveTask"
                >
                  {{ isNewTask ? 'Create Task' : 'Save' }}
                </v-btn>
                <v-btn
                  color="grey"
                  :disabled="editLoading"
                  prepend-icon="mdi-close"
                  size="small"
                  variant="outlined"
                  @click="cancelEdit"
                >
                  Cancel
                </v-btn>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- Task Header Card -->
      <v-card class="mb-6" elevation="2">
        <v-card-title class="d-flex align-start justify-space-between">
          <div class="flex-1-1">
            <div v-if="task" class="d-flex align-center mb-2">
              <v-avatar class="me-3" :color="getPriorityColor(task.priority)" size="40">
                <v-icon color="white">{{ getTypeIcon(task.type) }}</v-icon>
              </v-avatar>
              <div class="flex-1-1">
                <!-- View Mode (when not editing) -->
                <div v-if="!isEditMode">
                  <div class="text-h5 font-weight-bold">{{ task.summary }}</div>
                  <div class="text-body-2 text-medium-emphasis">{{ task.task_id }}</div>
                </div>
                <!-- Summary and Task ID for Edit Mode are handled by TaskForm -->
                <div v-else>
                  <div class="text-h5 font-weight-bold">
                    {{ isNewTask ? 'Create New Task' : `Editing: ${taskFormData.summary}` }}
                  </div>
                  <div class="text-body-2 text-medium-emphasis">
                    {{ isNewTask ? 'Fill in the details below' : taskFormData.task_id }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <v-menu v-if="!isNewTask">
            <template #activator="{ props }">
              <v-btn
                v-bind="props"
                icon="mdi-dots-vertical"
                variant="text"
              />
            </template>
            <v-list>
              <v-list-item @click="updateStatus('Backlog')">
                <v-list-item-title>Move to Backlog</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('In Progress')">
                <v-list-item-title>Start Progress</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('Done')">
                <v-list-item-title>Mark Done</v-list-item-title>
              </v-list-item>
              <v-list-item @click="updateStatus('Blocked')">
                <v-list-item-title>Mark Blocked</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-card-title>

        <v-card-text>
          <!-- View Mode for Task Details and Timestamps -->
          <div v-if="!isEditMode && task">
            <v-row>
              <v-col cols="12" md="6">
                <div class="mb-4">
                  <h4 class="text-subtitle-1 font-weight-medium mb-2">Task Details</h4>
                  <v-table density="compact">
                    <tbody>
                      <tr>
                        <td class="font-weight-medium">Type</td>
                        <td><v-chip size="small" variant="outlined">{{ task.type }}</v-chip></td>
                      </tr>
                      <tr>
                        <td class="font-weight-medium">Priority</td>
                        <td><v-chip :color="getPriorityColor(task.priority)" size="small">{{ task.priority }}</v-chip></td>
                      </tr>
                      <tr>
                        <td class="font-weight-medium">Status</td>
                        <td><v-chip :color="getStatusColor(task.status)" size="small" variant="tonal">{{ task.status }}</v-chip></td>
                      </tr>
                      <tr v-if="task.estimated_effort">
                        <td class="font-weight-medium">Estimated Effort</td>
                        <td>{{ task.estimated_effort }}</td>
                      </tr>
                      <tr v-if="task.epic">
                        <td class="font-weight-medium">Epic</td>
                        <td><v-chip color="primary" size="small" variant="text">{{ task.epic }}</v-chip></td>
                      </tr>
                    </tbody>
                  </v-table>
                </div>
              </v-col>
              <v-col cols="12" md="6">
                <div class="mb-4">
                  <h4 class="text-subtitle-1 font-weight-medium mb-2">Timestamps</h4>
                  <v-table density="compact">
                    <tbody>
                      <tr>
                        <td class="font-weight-medium">Created</td>
                        <td>{{ formatDate(task.created_at) }}</td>
                      </tr>
                      <tr>
                        <td class="font-weight-medium">Updated</td>
                        <td>{{ formatDate(task.updated_at) }}</td>
                      </tr>
                    </tbody>
                  </v-table>
                </div>
              </v-col>
            </v-row>
          </div>

          <!-- TaskForm for Edit Mode -->
          <TaskForm
            v-if="isEditMode"
            v-model="taskFormData"
            :existing-tasks="tasksStore.tasks"
            :is-new="isNewTask"
          />
        </v-card-text>
      </v-card>

      <!-- Linked Tasks (Display only, editing is in TaskForm) -->
      <v-card v-if="!isEditMode && task.linked_tasks && task.linked_tasks.length > 0" class="mb-6" elevation="2">
        <v-card-title>
          <v-icon class="me-2">mdi-link-variant</v-icon>
          Linked Tasks ({{ task.linked_tasks.length }})
        </v-card-title>
        <v-card-text>
          <div class="d-flex flex-wrap ga-2">
            <v-chip
              v-for="linkedTaskItem in task.linked_tasks"
              :key="linkedTaskItem.task_id"
              class="cursor-pointer"
              variant="outlined"
              @click="navigateToTask(linkedTaskItem.task_id)"
            >
              <v-icon start>mdi-open-in-new</v-icon>
              {{ linkedTaskItem.task_id }} ({{ linkedTaskItem.linkType }})
            </v-chip>
          </div>
        </v-card-text>
      </v-card>

      <!-- Task Description (Display only, editing is in TaskForm) -->
      <v-card v-if="!isEditMode" elevation="2">
        <v-card-title>
          <v-icon class="me-2">mdi-text-box-outline</v-icon>
          Description
          <v-spacer />
          <v-btn-toggle
            v-model="viewMode"
            density="compact"
            mandatory
            variant="outlined"
          >
            <v-btn icon="mdi-eye" title="Rendered View" value="rendered" />
            <v-btn icon="mdi-code-tags" title="Markdown Source" value="markdown" />
          </v-btn-toggle>
        </v-card-title>
        <v-card-text v-if="task" class="task-description">
          <div v-if="!task.description" class="text-center py-8">
            <v-icon class="mb-3" color="grey-lighten-2" size="48">mdi-text-box-remove-outline</v-icon>
            <p class="text-body-1 text-medium-emphasis">No description provided for this task.</p>
          </div>
          <div v-else-if="viewMode === 'rendered'" class="markdown-content">
            <MdPreview :model-value="task.description" preview-theme="vuepress" />
          </div>
          <div v-else class="markdown-source">
            <pre class="text-body-2"><code>{{ task.description }}</code></pre>
          </div>
        </v-card-text>
      </v-card>
    </div>

    <!-- Delete Confirmation Dialog (remains in parent) -->
    <v-dialog
      v-model="showDeleteDialog"
      max-width="500"
      persistent
    >
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon class="me-3" color="error">mdi-alert-circle</v-icon>
          <span class="text-h6">Delete Task</span>
        </v-card-title>

        <v-card-text>
          <p class="text-body-1 mb-4">
            Are you sure you want to delete this task? This action cannot be undone.
          </p>

          <v-card class="pa-3" color="grey-lighten-5" variant="outlined">
            <div class="text-subtitle-2 font-weight-medium">{{ task?.summary }}</div>
            <div class="text-body-2 text-medium-emphasis">{{ task?.task_id }}</div>
          </v-card>
        </v-card-text>

        <v-card-actions class="justify-end pa-4">
          <v-btn
            color="grey"
            :disabled="deleteLoading"
            variant="text"
            @click="closeDeleteDialog"
          >
            Cancel
          </v-btn>

          <v-btn
            color="error"
            :loading="deleteLoading"
            prepend-icon="mdi-delete"
            variant="elevated"
            @click="confirmDeleteTask"
          >
            Delete Task
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Status Update Snackbar -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="3000"
    >
      {{ snackbarMessage }}
      <template #actions>
        <v-btn
          color="white"
          variant="text"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
  import { MdPreview } from 'md-editor-v3'
  import { onMounted, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import TaskForm from '@/components/tasks/TaskForm.vue' // Import TaskForm
  import { useTasksStore } from '@/stores/tasks'
  import {
    formatDate as formatDateUtil,
    getPriorityColor as getPriorityColorUtil,
    getStatusColor as getStatusColorUtil,
    getTypeIcon as getTypeIconUtil,
  } from '@/utils/taskDisplayUtils' // Import display utils

  import 'md-editor-v3/lib/style.css'

  const route = useRoute()
  const router = useRouter()
  const tasksStore = useTasksStore()

  // Reactive data
  const task = ref(null) // This will hold the loaded task for display or the base for a new task
  const loading = ref(false)
  const error = ref(null)
  const viewMode = ref('rendered') // For markdown preview
  const showSnackbar = ref(false)
  const snackbarMessage = ref('')
  const snackbarColor = ref('success')

  // Edit mode state
  const isEditMode = ref(false)
  const editLoading = ref(false)
  const isNewTask = ref(false) // True if route.params.id is 'new'

  // Delete state
  const showDeleteDialog = ref(false)
  const deleteLoading = ref(false)

  // This will hold the data being edited by TaskForm.vue
  // It's initialized when entering edit mode or creating a new task.
  const taskFormData = ref({
    task_id: '',
    summary: '',
    description: '',
    type: 'Task',
    priority: 'Medium',
    status: 'Backlog',
    estimated_effort: 'Medium',
    epic: '',
    linked_tasks: [],
    project_id: '', // Required field for TaskForm component
  })

  // Use imported utils
  const getPriorityColor = priority => getPriorityColorUtil(priority)
  const getStatusColor = status => getStatusColorUtil(status)
  const getTypeIcon = type => getTypeIconUtil(type)
  const formatDate = dateString => formatDateUtil(dateString)

  // Methods
  const fetchTask = async taskId => {
    loading.value = true
    error.value = null

    try {
      const foundTask = await tasksStore.getTaskById(taskId)

      if (foundTask) {
        task.value = foundTask
      } else {
        error.value = `Task with ID "${taskId}" not found`
      }
    } catch (error_) {
      error.value = `Failed to load task: ${error_.message}`
      console.error('Error fetching task:', error_)
    } finally {
      loading.value = false
    }
  }

  const updateStatus = async newStatus => {
    if (!task.value) return

    try {
      await tasksStore.updateTaskStatus(task.value.task_id, newStatus)

      // Update local task object
      task.value.status = newStatus
      task.value.updated_at = new Date().toISOString()

      // Show success message
      snackbarMessage.value = `Task status updated to "${newStatus}"`
      snackbarColor.value = 'success'
      showSnackbar.value = true
    } catch (error) {
      snackbarMessage.value = `Failed to update task status: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error('Failed to update task status:', error)
    }
  }

  const navigateToTask = async taskId => {
    if (taskId === task.value?.task_id) return // Don't navigate to self

    await router.push(`/tasks/${taskId}`)
  }

  // Helper functions for autocomplete icons (getTaskTypeIcon, getPriorityIcon) are now in TaskForm.vue
  // Autocomplete methods (onAutocompleteSearch, onAutocompleteSelect) are now in TaskForm.vue
  // Task linking methods (addLinkedTask, removeLinkedTask) are now in TaskForm.vue
  // generateNewTaskId is now in TaskForm.vue

  // Delete methods
  const openDeleteDialog = () => {
    showDeleteDialog.value = true
  }

  const closeDeleteDialog = () => {
    showDeleteDialog.value = false
  }

  const confirmDeleteTask = async () => {
    if (!task.value || isNewTask.value) return

    deleteLoading.value = true

    try {
      const success = await tasksStore.deleteTask(task.value.task_id)

      if (success) {
        // Show success message
        snackbarMessage.value = 'Task deleted successfully'
        snackbarColor.value = 'success'
        showSnackbar.value = true

        // Close dialog
        showDeleteDialog.value = false

        // Navigate back to task list
        await router.push('/')
      } else {
        throw new Error('Failed to delete task')
      }
    } catch (error) {
      snackbarMessage.value = `Failed to delete task: ${error.message}`
      snackbarColor.value = 'error'
      showSnackbar.value = true
      console.error('Failed to delete task:', error)
    } finally {
      deleteLoading.value = false
    }
  }

  // Edit mode methods
  const enterEditMode = () => {
    if (!task.value && !isNewTask.value) return // Guard against no task loaded

    // Populate taskFormData with current task data or defaults for new
    taskFormData.value = {
      task_id: isNewTask.value ? '' : task.value?.task_id || '', // TaskForm handles ID generation for new
      summary: isNewTask.value ? '' : task.value?.summary || '',
      description: isNewTask.value ? '' : task.value?.description || '',
      type: isNewTask.value ? 'Task' : task.value?.type || 'Task',
      priority: isNewTask.value ? 'Medium' : task.value?.priority || 'Medium',
      status: isNewTask.value ? 'Backlog' : task.value?.status || 'Backlog',
      estimated_effort: isNewTask.value ? 'Medium' : task.value?.estimated_effort || 'Medium',
      epic: isNewTask.value ? '' : task.value?.epic || '',
      linked_tasks: isNewTask.value ? [] : [...(task.value?.linked_tasks || [])],
      project_id: isNewTask.value ? '' : task.value?.project_id || '', // Required field
    }
    isEditMode.value = true
  }

  const cancelEdit = () => {
    if (isNewTask.value) {
      router.push('/')
    } else {
      isEditMode.value = false
      // No need to reset taskFormData here, as it will be repopulated
      // if/when enterEditMode is called again with the original task data.
    }
  }

  const saveTask = async () => {
    // Basic validation (TaskForm should ideally have more robust validation)
    if (!taskFormData.value.summary || taskFormData.value.summary.length < 3) {
      snackbarMessage.value = 'Summary is required and must be at least 3 characters.'
      snackbarColor.value = 'error'
      showSnackbar.value = true
      return
    }

    if (isNewTask.value) {
      if (!taskFormData.value.task_id) {
        snackbarMessage.value = 'Task ID is required for new tasks. Please generate or enter one.'
        snackbarColor.value = 'error'
        showSnackbar.value = true
        return
      }
      const validationError = tasksStore.validateTaskId(taskFormData.value.task_id)
      if (validationError) {
        snackbarMessage.value = validationError
        snackbarColor.value = 'error'
        showSnackbar.value = true
        return
      }
    }

    editLoading.value = true
    try {
      let savedTaskResponse // Use a different variable name to avoid conflict with 'task' ref
      const currentRouteId = route.params.id // Capture current route ID before potential navigation

      if (isNewTask.value) {
        savedTaskResponse = await tasksStore.addTask({ ...taskFormData.value })
      } else {
        // For updates, do not send task_id in the payload, it's identified by URL/original task.
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { task_id, ...updateData } = taskFormData.value
        savedTaskResponse = await tasksStore.updateTask(task.value.task_id, updateData)
      }

      if (savedTaskResponse) {
        task.value = { ...savedTaskResponse } // Update local task with response
        isEditMode.value = false
        // Only reset isNewTask if it was true, to allow saving multiple times on an existing task page
        const wasNewTask = isNewTask.value
        if (wasNewTask) isNewTask.value = false

        snackbarMessage.value = `Task ${wasNewTask ? 'created' : 'updated'} successfully.`
        snackbarColor.value = 'success'
        // If it was a new task, navigate to its new ID page
        if (currentRouteId === 'new' && savedTaskResponse.task_id) {
          router.replace(`/tasks/${savedTaskResponse.task_id}`) // Use replace to avoid history stack issues
        }
      } else {
        throw new Error(`Failed to ${isNewTask.value ? 'create' : 'update'} task.`)
      }
    } catch (error_) {
      snackbarMessage.value = `Error: ${error_.message}`
      snackbarColor.value = 'error'
      console.error('Failed to save task:', error_)
    } finally {
      editLoading.value = false
      showSnackbar.value = true
    }
  }

  // Initialize new task or fetch existing
  const initializeTaskPage = async () => {
    const taskIdParam = route.params.id
    loading.value = true // Set loading true at the beginning
    error.value = null // Reset error

    if (taskIdParam === 'new') {
      isNewTask.value = true
      isEditMode.value = true // Start in edit mode for new tasks
      // Set up a placeholder 'task' object for UI consistency until form is fully managed
      task.value = {
        task_id: '', // Will be set by form
        summary: 'New Task', // Placeholder
        description: '',
        type: 'Task',
        priority: 'Medium',
        status: 'Backlog',
        estimated_effort: 'Medium',
        epic: '',
        linked_tasks: [],
        project_id: '', // Required field
        created_at: new Date().toISOString(), // Placeholder
        updated_at: new Date().toISOString(), // Placeholder
      }
      // Initialize taskFormData for the TaskForm component
      taskFormData.value = {
        task_id: '', // TaskForm will handle generation/input
        summary: '',
        description: '',
        type: 'Task',
        priority: 'Medium',
        status: 'Backlog',
        estimated_effort: 'Medium',
        epic: '',
        linked_tasks: [],
        project_id: '', // Required field for TaskForm component
      }
      loading.value = false // Not actually loading from DB for new
    } else if (taskIdParam) {
      isNewTask.value = false
      isEditMode.value = false // Start in view mode for existing tasks
      await fetchTask(taskIdParam) // This sets loading.value internally
    } else {
      error.value = 'Task ID is missing.' // Handle cases where ID might be undefined
      loading.value = false
    }
  }

  onMounted(() => {
    initializeTaskPage()
  })

  // Watch for route changes to re-initialize if navigating between task pages
  watch(() => route.params.id, newId => {
    // Only re-initialize if the ID actually changes to a valid new ID or 'new'
    if (newId && newId !== task.value?.task_id && !(newId === 'new' && isNewTask.value)) {
      // Reset state before initializing for a new/different task
      task.value = null // Clear current task
      // isEditMode.value = false; // Reset edit mode
      // isNewTask.value = (newId === 'new'); // Update isNewTask state
      initializeTaskPage() // This will set loading, error, task, isNewTask, isEditMode
    }
  }, { immediate: false }) // 'immediate: false' to avoid running on initial mount if not needed.
</script>

<style scoped>
.task-description {
  max-width: none;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.markdown-content :deep(h1) {
  font-size: 2rem;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.3rem;
}

.markdown-content :deep(p) {
  margin-bottom: 1rem;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.markdown-content :deep(li) {
  margin-bottom: 0.25rem;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #2196f3;
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  font-style: italic;
}

.markdown-content :deep(code) {
  background-color: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: #f5f5f5;
  font-weight: 600;
}

.markdown-content :deep(a) {
  color: #2196f3;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-source {
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 1rem;
}

.markdown-source pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.markdown-source code {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.cursor-pointer {
  cursor: pointer;
}

.markdown-preview {
  max-height: 300px;
  overflow-y: auto;
}

.markdown-preview .markdown-content {
  line-height: 1.6;
}

.markdown-preview .markdown-content :deep(h1),
.markdown-preview .markdown-content :deep(h2),
.markdown-preview .markdown-content :deep(h3),
.markdown-preview .markdown-content :deep(h4),
.markdown-preview .markdown-content :deep(h5),
.markdown-preview .markdown-content :deep(h6) {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.markdown-preview .markdown-content :deep(p) {
  margin-bottom: 0.5rem;
}

.markdown-preview .markdown-content :deep(ul),
.markdown-preview .markdown-content :deep(ol) {
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.markdown-preview .markdown-content :deep(code) {
  background-color: #f5f5f5;
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-preview .markdown-content :deep(pre) {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.markdown-preview .markdown-content :deep(blockquote) {
  border-left: 4px solid #2196f3;
  margin: 0.5rem 0;
  padding: 0.25rem 0.5rem;
  background-color: #f5f5f5;
  font-style: italic;
}
</style>
