<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="text-center pa-8">
          <v-icon size="120" color="error" class="mb-4">
            mdi-lock-alert
          </v-icon>
          
          <v-card-title class="text-h4 font-weight-bold mb-4">
            Access Denied
          </v-card-title>
          
          <v-card-text class="text-body-1 mb-6">
            <p class="mb-4">
              You don't have permission to access this page.
            </p>
            <p class="text-medium-emphasis">
              If you believe this is an error, please contact your administrator
              or try logging in with a different account.
            </p>
          </v-card-text>
          
          <v-card-actions class="justify-center">
            <v-btn
              color="primary"
              variant="elevated"
              prepend-icon="mdi-home"
              @click="goHome"
            >
              Go to Dashboard
            </v-btn>
            
            <v-btn
              color="secondary"
              variant="outlined"
              prepend-icon="mdi-logout"
              @click="logout"
            >
              Logout
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const goHome = () => {
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  } else {
    router.push('/login')
  }
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
    // Force redirect to login even if logout fails
    router.push('/login')
  }
}
</script>

<style scoped>
.fill-height {
  min-height: 100vh;
}
</style>
