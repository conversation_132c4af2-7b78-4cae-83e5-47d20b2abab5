<template>
  <v-container class="pa-6" fluid>
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">Upload Tasks</h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Import your project tasks from JSON files
            </p>
          </div>
          <v-chip
            v-if="tasksStore.tasks.length > 0"
            color="primary"
            size="large"
            variant="elevated"
          >
            {{ tasksStore.tasks.length }} Tasks in Database
          </v-chip>
        </div>
      </v-col>
    </v-row>

    <!-- Info Card -->
    <TaskUploadInfo />

    <!-- Project Selection -->
    <ProjectSelector
      v-model="selectedProjectId"
      class="mb-4"
      @project-changed="onProjectChanged"
    />

    <!-- File Upload Section -->
    <v-row class="mb-6">
      <v-col>
        <v-card class="mb-4" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon class="me-3" color="primary">mdi-upload</v-icon>
            Upload Tasks JSON File
          </v-card-title>

          <v-card-text>
            <div class="text-center pa-6">
              <v-file-input
                v-model="selectedFile"
                accept=".json"
                class="mb-4"
                :disabled="tasksStore.uploadStatus === 'processing'"
                label="Select JSON file"
                :loading="tasksStore.loading"
                prepend-icon="mdi-file-document"
                variant="outlined"
                @change="handleFileSelect"
              />

              <!-- Upload Progress -->
              <div v-if="tasksStore.uploadStatus === 'processing'" class="mb-4">
                <v-progress-linear
                  color="primary"
                  height="8"
                  :model-value="tasksStore.uploadProgress"
                  rounded
                />
                <p class="text-caption mt-2">Processing tasks...</p>
              </div>

              <!-- Upload Button -->
              <v-btn
                color="primary"
                :disabled="!selectedFile || tasksStore.uploadStatus === 'processing'"
                :loading="tasksStore.uploadStatus === 'processing'"
                size="large"
                variant="elevated"
                @click="processUpload"
              >
                <v-icon start>mdi-database-import</v-icon>
                Process Upload
              </v-btn>

              <!-- Clear Database Button -->
              <v-btn
                v-if="tasksStore.tasks.length > 0"
                class="ml-4"
                color="error"
                variant="outlined"
                @click="showClearDialog = true"
              >
                <v-icon start>mdi-delete</v-icon>
                Clear Database
              </v-btn>
            </div>

            <!-- Upload Result -->
            <v-alert
              v-if="tasksStore.uploadStatus === 'success' && tasksStore.lastUploadResult"
              class="mt-4"
              type="success"
              variant="tonal"
            >
              <v-alert-title>Upload Successful!</v-alert-title>
              Successfully imported {{ tasksStore.lastUploadResult.successful }} tasks.
              <div v-if="tasksStore.lastUploadResult.failed > 0" class="mt-2">
                <strong>{{ tasksStore.lastUploadResult.failed }} tasks failed:</strong>
                <ul class="mt-1">
                  <li v-for="error in tasksStore.lastUploadResult.errors" :key="error">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </v-alert>

            <!-- Error Display -->
            <v-alert
              v-if="tasksStore.error"
              class="mt-4"
              closable
              type="error"
              variant="tonal"
              @click:close="tasksStore.error = null"
            >
              {{ tasksStore.error }}
            </v-alert>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Statistics Cards -->
    <DashboardStats v-if="tasksStore.tasks.length > 0" />

    <!-- Success Actions -->
    <v-row v-if="tasksStore.uploadStatus === 'success'">
      <v-col>
        <v-card class="text-center pa-6" elevation="1">
          <v-icon class="mb-4" color="success" size="64">
            mdi-check-circle
          </v-icon>
          <h2 class="text-h5 mb-4">Tasks Uploaded Successfully!</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Your tasks have been imported and are ready to manage.
          </p>
          <v-btn
            color="primary"
            size="large"
            variant="elevated"
            @click="$router.push('/')"
          >
            <v-icon start>mdi-format-list-checks</v-icon>
            View Tasks
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <v-row v-else-if="tasksStore.tasks.length === 0 && !tasksStore.loading && tasksStore.uploadStatus === 'idle'">
      <v-col>
        <v-card class="text-center pa-12" elevation="1">
          <v-icon class="mb-4" color="grey-lighten-2" size="120">
            mdi-file-upload-outline
          </v-icon>
          <h2 class="text-h5 mb-4">Ready to Import Tasks</h2>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Upload a JSON file containing your tasks to get started with task management.
          </p>
        </v-card>
      </v-col>
    </v-row>

    <!-- Clear Database Confirmation Dialog -->
    <v-dialog v-model="showClearDialog" max-width="500">
      <v-card>
        <v-card-title class="d-flex align-center">
          <v-icon class="me-3" color="error">mdi-alert</v-icon>
          Clear Database
        </v-card-title>

        <v-card-text>
          Are you sure you want to clear all tasks from the database? This action cannot be undone.
        </v-card-text>

        <v-card-actions>
          <v-spacer />
          <v-btn @click="showClearDialog = false">Cancel</v-btn>
          <v-btn color="error" @click="clearDatabase">Clear All</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Loading Overlay -->
    <v-overlay
      v-model="tasksStore.loading"
      class="align-center justify-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import DashboardStats from '@/components/dashboard/DashboardStats.vue' // Import DashboardStats
  import ProjectSelector from '@/components/projects/ProjectSelector.vue'
  import TaskUploadInfo from '@/components/TaskUploadInfo.vue'
  import { useProjectsStore } from '@/stores/projects'
  import { useTasksStore } from '@/stores/tasks'
  import { useAuthStore } from '@/stores/auth'

  // Stores
  const tasksStore = useTasksStore()
  const projectsStore = useProjectsStore()
  const authStore = useAuthStore()

  // Reactive data
  const selectedFile = ref(null)
  const selectedProjectId = ref(null)
  const showClearDialog = ref(false)

  // Methods
  const handleFileSelect = () => {
    tasksStore.resetUploadState()
  }

  const processUpload = async () => {
    if (!selectedFile.value) return

    try {
      const fileContent = await readFileAsText(selectedFile.value)
      const jsonData = JSON.parse(fileContent)

      // Get the selected project ID (should be set by ProjectSelector)
      const projectId = projectsStore.selectedProject?.id
      if (!projectId) {
        throw new Error('Please select a project before uploading tasks')
      }

      await tasksStore.uploadTasksFromJson(jsonData, projectId)

      // Clear file input
      selectedFile.value = null
    } catch (error) {
      tasksStore.error = error instanceof SyntaxError ? 'Invalid JSON file. Please check the file format.' : error.message
      tasksStore.uploadStatus = 'error'
    }
  }

  const readFileAsText = async file => {
    try {
      return await file.text()
    } catch {
      throw new Error('Failed to read file')
    }
  }

  const clearDatabase = async () => {
    try {
      await tasksStore.clearAllTasks()
      showClearDialog.value = false
    } catch (error) {
      console.error('Failed to clear database:', error)
    }
  }

  const onProjectChanged = (project) => {
    console.log('Project changed to:', project?.name || 'None')
  }

  // Lifecycle
  onMounted(async () => {
    // Only fetch data if user is authenticated
    if (authStore.isAuthenticated) {
      // Fetch both projects and tasks
      await Promise.all([
        projectsStore.fetchProjects(),
        tasksStore.fetchTasks(),
      ])

      // Set initial project selection
      if (projectsStore.selectedProject) {
        selectedProjectId.value = projectsStore.selectedProject.id
      } else if (projectsStore.projects.length > 0) {
        selectedProjectId.value = projectsStore.projects[0].id
        projectsStore.selectProject(projectsStore.projects[0])
      }
    }
  })
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}
</style>
