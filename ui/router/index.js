/**
 * router/index.js
 *
 * Automatic routes for `./ui/pages/*.vue`
 */

// Composables
import { createRouter, createWebHistory } from 'vue-router/auto'
import { setupLayouts } from 'virtual:generated-layouts'
import { routes } from 'vue-router/auto-routes'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: setupLayouts(routes),
})

// Routes that should redirect to dashboard if already authenticated (guest-only)
const guestOnlyRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password'
]

// Public routes that don't require authentication
const publicRoutes = [
  '/help'
]

// All other routes require authentication by default

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Initialize auth store if not already initialized
  if (!authStore.initialized) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.warn('Auth initialization failed:', error)
      // Clear auth state on initialization failure
      authStore.clearAuthState()
    }
  }

  const isAuthenticated = authStore.isAuthenticated
  const isGuestOnlyRoute = guestOnlyRoutes.some(route => to.path.startsWith(route))
  const isPublicRoute = publicRoutes.some(route => to.path.startsWith(route))

  // All routes except guest-only and public routes require authentication
  const requiresAuth = !isGuestOnlyRoute && !isPublicRoute

  // Redirect authenticated users away from guest-only routes
  if (isAuthenticated && isGuestOnlyRoute) {
    // Redirect to originally requested page or default to dashboard
    const redirectTo = to.query.redirect || '/dashboard'
    next(redirectTo)
    return
  }

  // Redirect unauthenticated users away from protected routes
  if (!isAuthenticated && requiresAuth) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // Allow navigation
  next()
})

// Workaround for https://github.com/vitejs/vite/issues/11804
router.onError((err, to) => {
  if (err?.message?.includes?.('Failed to fetch dynamically imported module')) {
    if (localStorage.getItem('vuetify:dynamic-reload')) {
      console.error('Dynamic import error, reloading page did not fix it', err)
    } else {
      console.log('Reloading page to fix dynamic import error')
      localStorage.setItem('vuetify:dynamic-reload', 'true')
      location.assign(to.fullPath)
    }
  } else {
    console.error(err)
  }
})

router.isReady().then(() => {
  localStorage.removeItem('vuetify:dynamic-reload')
})

export default router
