# Track Tasks API Documentation

## Overview

The Track Tasks API provides comprehensive RESTful endpoints for managing tasks, projects, and user authentication. Built with Express.js and integrated with PocketBase for data persistence, the API offers secure, scalable task management functionality.

**Base URL:** `http://localhost:3001/api`  
**Version:** 1.0.0  
**Authentication:** <PERSON><PERSON> Token Required

## Table of Contents

1. [Authentication](#authentication)
2. [Tasks API](#tasks-api)
3. [Projects API](#projects-api)
4. [Data Models](#data-models)
5. [Error Handling](#error-handling)
6. [Security & Rate Limiting](#security--rate-limiting)
7. [Examples](#examples)

---

## Authentication

All API endpoints require authentication via <PERSON><PERSON> token in the Authorization header.

### Headers Required
```http
Authorization: Bearer <token>
Content-Type: application/json
```

### Authentication Endpoints

#### POST /api/auth/login
Authenticate user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "rememberMe": false
}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "user",
      "verified": true
    },
    "token": "jwt-token-string",
    "refreshToken": "refresh-token-string"
  },
  "message": "Login successful"
}
```

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "passwordConfirm": "securePassword123"
}
```

#### POST /api/auth/logout
Logout current user and invalidate token.

**Headers:** `Authorization: Bearer <token>`

#### POST /api/auth/refresh
Refresh authentication token.

**Request Body:**
```json
{
  "refreshToken": "refresh-token-string"
}
```

#### GET /api/auth/me
Get current user profile information.

**Headers:** `Authorization: Bearer <token>`

#### POST /api/auth/forgot-password
Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST /api/auth/reset-password
Complete password reset with token.

**Request Body:**
```json
{
  "token": "reset-token",
  "password": "newPassword123",
  "passwordConfirm": "newPassword123"
}
```

---

## Tasks API

### POST /api/tasks
Create a new task.

**Request Body:**
```json
{
  "summary": "Implement user authentication",
  "project_id": "project-uuid",
  "description": "Add JWT-based authentication system",
  "priority": "High",
  "type": "Feature",
  "epic": "User Management",
  "assigned_to": "<EMAIL>",
  "estimated_effort": "Large"
}
```

**Success Response (201):**
```json
{
  "id": "task-database-uuid",
  "task_id": "TASK-1234567890-ABC12",
  "summary": "Implement user authentication",
  "description": "Add JWT-based authentication system",
  "priority": "High",
  "type": "Feature",
  "status": "Backlog",
  "estimated_effort": "Large",
  "epic": "User Management",
  "assigned_to": "<EMAIL>",
  "project_id": "project-uuid",
  "linked_tasks": [],
  "created_at": "2025-01-17T10:30:00Z",
  "updated_at": "2025-01-17T10:30:00Z"
}
```

### GET /api/tasks
Retrieve tasks with optional filtering and pagination.

**Query Parameters:**
- `project_id` (string): Filter by project ID
- `status` (string): Filter by status (Backlog, In Progress, Done, etc.)
- `priority` (string): Filter by priority (Low, Medium, High, Critical)
- `type` (string): Filter by type (Task, Bug, Feature, Epic)
- `epic` (string): Filter by epic name
- `assigned_to` (string): Filter by assignee email
- `search` (string): Search in summary and description
- `limit` (number): Number of results per page (default: 50, max: 100)
- `offset` (number): Number of results to skip (default: 0)

**Example Request:**
```http
GET /api/tasks?project_id=proj-123&status=In%20Progress&limit=10&offset=0
```

**Success Response (200):**
```json
{
  "tasks": [
    {
      "id": "task-uuid",
      "task_id": "TASK-001",
      "summary": "Task summary",
      "status": "In Progress",
      // ... other task fields
    }
  ],
  "pagination": {
    "total": 25,
    "limit": 10,
    "offset": 0,
    "hasMore": true
  }
}
```

### GET /api/tasks/:task_id
Retrieve a specific task by its task ID.

**Path Parameters:**
- `task_id` (string): The task identifier (e.g., "TASK-001")

**Success Response (200):**
```json
{
  "id": "task-database-uuid",
  "task_id": "TASK-001",
  "summary": "Task summary",
  // ... complete task object
}
```

### PUT /api/tasks/:task_id
Update an existing task.

**Path Parameters:**
- `task_id` (string): The task identifier

**Request Body (partial updates supported):**
```json
{
  "summary": "Updated task summary",
  "status": "In Progress",
  "priority": "High",
  "assigned_to": "<EMAIL>"
}
```

### PATCH /api/tasks/:task_id/status
Update only the status of a task.

**Request Body:**
```json
{
  "status": "Done"
}
```

### DELETE /api/tasks/:task_id
Delete a task (Admin only).

**Success Response (204):** No content

---

## Projects API

### POST /api/projects
Create a new project.

**Request Body:**
```json
{
  "name": "Mobile App Development",
  "description": "iOS and Android mobile application project"
}
```

### GET /api/projects
Retrieve all projects.

**Success Response (200):**
```json
{
  "projects": [
    {
      "id": "project-uuid",
      "name": "Mobile App Development",
      "description": "iOS and Android mobile application project",
      "created_at": "2025-01-17T10:30:00Z",
      "updated_at": "2025-01-17T10:30:00Z"
    }
  ]
}
```

### GET /api/projects/:project_id
Retrieve a specific project.

### PUT /api/projects/:project_id
Update a project.

### DELETE /api/projects/:project_id
Delete a project (Admin only).

### GET /api/projects/:project_id/tasks
Retrieve all tasks for a specific project.

**Query Parameters:**
- `status` (string): Filter tasks by status
- `priority` (string): Filter tasks by priority
- `limit` (number): Limit results
- `offset` (number): Pagination offset

---

## Data Models

### Task Object
```json
{
  "id": "string (UUID)",
  "task_id": "string (unique identifier)",
  "summary": "string (1-500 chars, required)",
  "description": "string (max 5000 chars, optional)",
  "priority": "enum: Low|Medium|High|Critical",
  "type": "enum: Task|Bug|Feature|Epic",
  "status": "enum: Backlog|In Progress|Done|Cancelled",
  "estimated_effort": "enum: Small|Medium|Large|Extra Large",
  "epic": "string (max 100 chars, optional)",
  "assigned_to": "string (email, max 100 chars, optional)",
  "project_id": "string (UUID, required)",
  "linked_tasks": "array of objects",
  "created_at": "string (ISO 8601 datetime)",
  "updated_at": "string (ISO 8601 datetime)"
}
```

### Project Object
```json
{
  "id": "string (UUID)",
  "name": "string (1-100 chars, required)",
  "description": "string (max 1000 chars, optional)",
  "created_at": "string (ISO 8601 datetime)",
  "updated_at": "string (ISO 8601 datetime)"
}
```

### User Object
```json
{
  "id": "string (UUID)",
  "email": "string (email format)",
  "name": "string",
  "role": "enum: user|admin",
  "verified": "boolean",
  "avatar": "string (URL, optional)",
  "created": "string (ISO 8601 datetime)",
  "updated": "string (ISO 8601 datetime)"
}
```

---

## Error Handling

All error responses follow a standardized format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "timestamp": "2025-01-17T10:30:00Z",
    "request_id": "req-uuid",
    "details": "Additional error details (optional)"
  }
}
```

### HTTP Status Codes

| Status | Description | Common Scenarios |
|--------|-------------|------------------|
| 200 | OK | Successful GET, PUT, PATCH |
| 201 | Created | Successful POST |
| 204 | No Content | Successful DELETE |
| 400 | Bad Request | Validation errors, malformed JSON |
| 401 | Unauthorized | Missing/invalid token, expired session |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource doesn't exist |
| 409 | Conflict | Duplicate entries |
| 422 | Unprocessable Entity | Business logic validation failed |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side errors |

### Common Error Codes

- `UNAUTHORIZED`: Authentication required or invalid token
- `FORBIDDEN`: Insufficient permissions for operation
- `NOT_FOUND`: Requested resource not found
- `VALIDATION_ERROR`: Request validation failed
- `CONFLICT`: Resource already exists
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `AUTH_RATE_LIMIT_EXCEEDED`: Too many authentication attempts
- `SESSION_NOT_FOUND`: Session expired or invalid
- `INVALID_TOKEN`: JWT token is invalid
- `TOKEN_EXPIRED`: JWT token has expired

---

## Security & Rate Limiting

### Authentication Security
- JWT tokens with configurable expiration
- Session management with automatic cleanup
- CSRF protection for authenticated sessions
- Secure password requirements (8+ chars, mixed case, numbers)

### Rate Limiting
- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 5 attempts per 15 minutes per IP
- **Password Reset**: Progressive delays for repeated attempts

### Security Headers
- Strict Transport Security (HSTS) in production
- Content Security Policy
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Comprehensive CORS configuration

### CORS Configuration
The API supports cross-origin requests with proper CORS headers configured for frontend integration.

---

## Examples

### Complete Task Management Workflow

#### 1. Login
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

#### 2. Create Project
```bash
curl -X POST http://localhost:3001/api/projects \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Website Redesign",
    "description": "Complete redesign of company website"
  }'
```

#### 3. Create Task
```bash
curl -X POST http://localhost:3001/api/tasks \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "summary": "Design new homepage layout",
    "project_id": "PROJECT_UUID",
    "priority": "High",
    "type": "Feature",
    "epic": "Homepage Redesign"
  }'
```

#### 4. Update Task Status
```bash
curl -X PATCH http://localhost:3001/api/tasks/TASK-001/status \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "In Progress"
  }'
```

#### 5. List Tasks with Filters
```bash
curl -X GET "http://localhost:3001/api/tasks?project_id=PROJECT_UUID&status=In%20Progress&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Frontend Integration Example (JavaScript)

```javascript
class TrackTasksAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error.message);
    }

    return response.json();
  }

  // Task methods
  async createTask(taskData) {
    return this.request('/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData)
    });
  }

  async getTasks(filters = {}) {
    const params = new URLSearchParams(filters);
    return this.request(`/tasks?${params}`);
  }

  async updateTask(taskId, updates) {
    return this.request(`/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
  }

  async updateTaskStatus(taskId, status) {
    return this.request(`/tasks/${taskId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status })
    });
  }

  // Project methods
  async createProject(projectData) {
    return this.request('/projects', {
      method: 'POST',
      body: JSON.stringify(projectData)
    });
  }

  async getProjects() {
    return this.request('/projects');
  }

  async getProjectTasks(projectId, filters = {}) {
    const params = new URLSearchParams(filters);
    return this.request(`/projects/${projectId}/tasks?${params}`);
  }
}

// Usage
const api = new TrackTasksAPI('http://localhost:3001/api', 'your-jwt-token');

// Create a task
const newTask = await api.createTask({
  summary: 'Implement API documentation',
  project_id: 'project-uuid',
  priority: 'Medium',
  type: 'Task'
});

// Get filtered tasks
const tasks = await api.getTasks({
  status: 'In Progress',
  priority: 'High',
  limit: 20
});
```

---

## Development Notes

### Environment Variables
```bash
# API Configuration
API_PORT=3001
NODE_ENV=development

# Database
POCKETBASE_URL=http://localhost:8090
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=admin123

# Security
JWT_SECRET=your-secret-key-change-in-production
```

### Testing
```bash
# Run all API tests
npm run test:api

# Start development server
npm run api:start

# Test authentication
curl -H "Authorization: Bearer admin-token" http://localhost:3001/api/tasks
```

For development purposes, you can use `admin-token` as the Bearer token to bypass authentication.

---

*This documentation covers the complete Track Tasks API. For additional support or feature requests, please refer to the project repository.*
