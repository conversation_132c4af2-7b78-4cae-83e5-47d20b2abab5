# Trace Logging System Guide

## Overview

The Track Tasks application includes a comprehensive trace logging system that provides detailed visibility into the complete request flow from UI to API endpoints. This system is designed to help with debugging, performance monitoring, and understanding application behavior.

## Features

- **Configurable Feature Flag**: Enable/disable trace logging without code changes
- **Hierarchical Logging**: Uses dash-greater-than notation to show call hierarchy
- **Full Request Flow**: Traces from UI components through HTTP client to API endpoints
- **Data Sanitization**: Automatically redacts sensitive information (passwords, tokens, etc.)
- **Performance Metrics**: Includes execution duration for all traced operations
- **Multiple Log Levels**: Support for error, warn, info, debug, and trace levels
- **Flexible Output**: JSON or text format options

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```bash
# Trace Logging Configuration
ENABLE_TRACE_LOGGING=false
TRACE_LOG_LEVEL=debug
TRACE_LOG_FORMAT=json
```

### Configuration Options

- **ENABLE_TRACE_LOGGING**: `true` or `false` - Master switch for trace logging
- **TRACE_LOG_LEVEL**: `error`, `warn`, `info`, `debug`, or `trace` - Minimum log level to output
- **TRACE_LOG_FORMAT**: `json` or `text` - Output format for trace logs

## Hierarchy Notation

The trace logging system uses a consistent hierarchy notation to show the call flow:

- `-> ` - Top-level calls (UI pages, API routes)
- `--> ` - Sub-calls (HTTP client, services)
- `---> ` - Nested sub-calls (utility functions, database operations)

## Log Types

### UI-Side Logging

- **UI_TRACE_ENTRY**: Function/method entry
- **UI_TRACE_EXIT**: Function/method exit with results
- **UI_TRACE_VUE_LIFECYCLE**: Vue component lifecycle hooks
- **UI_TRACE_STORE_ACTION**: Pinia store actions
- **UI_TRACE_HTTP_REQUEST**: Outgoing HTTP requests
- **UI_TRACE_HTTP_RESPONSE**: HTTP responses
- **UI_TRACE_ERROR**: Error conditions

### API-Side Logging

- **TRACE_ENTRY**: Function/endpoint entry
- **TRACE_EXIT**: Function/endpoint exit with results
- **TRACE_HTTP_REQUEST**: Incoming HTTP requests
- **TRACE_HTTP_RESPONSE**: Outgoing HTTP responses
- **TRACE_ERROR**: Error conditions

## Example Output

### JSON Format
```json
{
  "timestamp": "2025-01-25T10:30:45.123Z",
  "type": "UI_TRACE_HTTP_REQUEST",
  "level": "DEBUG",
  "hierarchy": "--> ",
  "name": "POST /api/auth/login",
  "method": "POST",
  "url": "/api/auth/login",
  "headers": {
    "content-type": "application/json",
    "authorization": "[REDACTED]"
  },
  "body": {
    "email": "<EMAIL>",
    "password": "[REDACTED]",
    "rememberMe": false
  }
}
```

### Text Format
```
[2025-01-25T10:30:45.123Z] UI_TRACE_HTTP_REQUEST --> POST /api/auth/login | {"method":"POST","url":"/api/auth/login","headers":{"authorization":"[REDACTED]"}}
```

## Usage Examples

### Enabling Trace Logging

1. **Development Environment**:
   ```bash
   ENABLE_TRACE_LOGGING=true
   TRACE_LOG_LEVEL=debug
   TRACE_LOG_FORMAT=json
   ```

2. **Production Debugging** (use sparingly):
   ```bash
   ENABLE_TRACE_LOGGING=true
   TRACE_LOG_LEVEL=error
   TRACE_LOG_FORMAT=text
   ```

3. **Disable Completely**:
   ```bash
   ENABLE_TRACE_LOGGING=false
   ```

### Viewing Logs

Trace logs are output to the console:

- **Browser**: Open Developer Tools → Console
- **API Server**: Check terminal/server logs
- **Production**: Configure log aggregation service

## Performance Impact

- **When Disabled**: Minimal overhead (simple boolean check)
- **When Enabled**: Low overhead for most operations
- **Data Sanitization**: Automatic removal of sensitive fields
- **Log Levels**: Higher levels (error, warn) have less overhead than debug/trace

## Security Considerations

### Automatic Data Sanitization

The system automatically redacts sensitive information:

- **Headers**: `authorization`, `cookie`, `x-csrf-token`, `x-api-key`
- **Body Fields**: `password`, `token`, `secret`, `key`, `auth`, `refreshToken`

### Production Usage

- Use higher log levels (error, warn) in production
- Consider log retention policies
- Ensure log aggregation services handle sensitive data appropriately
- Monitor log volume to prevent storage issues

## Troubleshooting

### Trace Logs Not Appearing

1. Check environment variables are set correctly
2. Verify `ENABLE_TRACE_LOGGING=true`
3. Check log level setting
4. Restart application after configuration changes

### Too Many Logs

1. Increase log level (debug → info → warn → error)
2. Use text format instead of JSON for smaller output
3. Disable trace logging in production

### Missing Trace Data

1. Ensure all components import trace logging utilities
2. Check for JavaScript errors preventing trace calls
3. Verify network connectivity for API traces

## Integration with Monitoring

The trace logging system can be integrated with monitoring solutions:

- **Log Aggregation**: ELK Stack, Splunk, CloudWatch
- **APM Tools**: New Relic, Datadog, Application Insights
- **Custom Dashboards**: Parse JSON logs for metrics and visualization

## Best Practices

1. **Development**: Enable with debug level for full visibility
2. **Staging**: Use info level for integration testing
3. **Production**: Use warn/error levels only, or disable completely
4. **Debugging**: Enable temporarily with specific log levels
5. **Performance Testing**: Disable to avoid skewing results
6. **Log Rotation**: Implement log rotation in production environments

## Future Enhancements

- Correlation IDs for distributed tracing
- Sampling for high-volume environments
- Custom trace contexts
- Integration with OpenTelemetry
- Real-time log streaming
- Trace filtering by component/module
