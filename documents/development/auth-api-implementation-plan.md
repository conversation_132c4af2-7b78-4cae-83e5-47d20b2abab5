# Authentication API Implementation Plan

## Overview
This document outlines the implementation plan for the missing authentication API endpoints as specified in the authentication requirements document (TR-010, API-001 through API-012).

## Endpoint Specifications

### 1. POST /api/auth/login (API-001)
**Purpose**: User login with email and password
**Request Schema**:
```json
{
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars)",
  "rememberMe": "boolean (optional, default false)"
}
```
**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "email": "string",
      "name": "string",
      "avatar": "string|null",
      "role": "string",
      "verified": "boolean"
    },
    "token": "string (JWT)",
    "refreshToken": "string"
  },
  "message": "Login successful"
}
```

### 2. POST /api/auth/register (API-002)
**Purpose**: User registration
**Request Schema**:
```json
{
  "name": "string (required, min 2 chars)",
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars, complexity rules)",
  "passwordConfirm": "string (required, must match password)"
}
```
**Success Response (201)**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "email": "string",
      "name": "string",
      "verified": "boolean"
    },
    "message": "Registration successful. Please check your email to verify your account."
  }
}
```

### 3. POST /api/auth/logout (API-003)
**Purpose**: User logout
**Request**: No body required (uses Authorization header)
**Success Response (200)**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### 4. POST /api/auth/refresh (API-004)
**Purpose**: Token refresh
**Request Schema**:
```json
{
  "refreshToken": "string (required)"
}
```
**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "token": "string (new JWT)",
    "refreshToken": "string (new refresh token)"
  }
}
```

### 5. POST /api/auth/forgot-password (API-005)
**Purpose**: Password reset request
**Request Schema**:
```json
{
  "email": "string (required, valid email)"
}
```
**Success Response (200)**:
```json
{
  "success": true,
  "message": "Password reset link sent to your email"
}
```

### 6. POST /api/auth/reset-password (API-006)
**Purpose**: Password reset completion
**Request Schema**:
```json
{
  "token": "string (required, reset token)",
  "password": "string (required, min 8 chars, complexity rules)",
  "passwordConfirm": "string (required, must match password)"
}
```
**Success Response (200)**:
```json
{
  "success": true,
  "message": "Password reset successful"
}
```

### 7. GET /api/auth/me (API-007)
**Purpose**: Get current user profile
**Request**: No body (uses Authorization header)
**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "email": "string",
      "name": "string",
      "avatar": "string|null",
      "role": "string",
      "verified": "boolean",
      "created": "string (ISO date)",
      "updated": "string (ISO date)"
    }
  }
}
```

### 8. PUT /api/auth/profile (API-008)
**Purpose**: Update user profile
**Request Schema**:
```json
{
  "name": "string (optional, min 2 chars)",
  "email": "string (optional, valid email)",
  "avatar": "string (optional, base64 or URL)"
}
```
**Success Response (200)**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "email": "string",
      "name": "string",
      "avatar": "string|null",
      "role": "string",
      "verified": "boolean"
    }
  },
  "message": "Profile updated successfully"
}
```

### 9. PUT /api/auth/password (API-009)
**Purpose**: Change password
**Request Schema**:
```json
{
  "currentPassword": "string (required)",
  "newPassword": "string (required, min 8 chars, complexity rules)",
  "newPasswordConfirm": "string (required, must match newPassword)"
}
```
**Success Response (200)**:
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

## Standardized Error Response Format (API-010, API-012)

All error responses will follow this format:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "object|string (optional, additional error details)",
    "timestamp": "string (ISO date)"
  }
}
```

### Error Codes:
- `VALIDATION_ERROR`: Input validation failed
- `UNAUTHORIZED`: Authentication required or invalid
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource already exists
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_SERVER_ERROR`: Server error
- `INVALID_CREDENTIALS`: Login failed
- `EMAIL_NOT_VERIFIED`: Email verification required
- `TOKEN_EXPIRED`: Token has expired
- `INVALID_TOKEN`: Token is invalid or malformed

## Integration Strategy

### PocketBase Service Integration
- Use existing `common/services/pocketbase.js` for all authentication operations
- Proxy requests through Express.js API endpoints
- Maintain compatibility with existing frontend implementation
- Handle PocketBase errors and convert to standardized API responses

### Middleware Integration
- Use existing `api/middleware/auth.js` for protected endpoints
- Apply authentication middleware to protected routes (GET /api/auth/me, PUT /api/auth/profile, PUT /api/auth/password)
- Use rate limiting for authentication endpoints
- Apply validation middleware to all endpoints

### Validation Rules
- Reuse existing validation patterns from `api/utils/validation.js`
- Add new validation rules specific to authentication endpoints
- Implement password complexity validation
- Add email format validation

## Implementation Files

### New Files to Create:
1. `api/routes/auth.js` - Authentication route handlers
2. `api/services/authService.js` - Authentication business logic
3. `test/unit/routes/auth.test.js` - Unit tests for auth routes
4. `test/unit/services/authService.test.js` - Unit tests for auth service

### Files to Modify:
1. `api/server.js` - Register authentication routes
2. `api/utils/validation.js` - Add authentication validation rules

## Security Considerations

### Rate Limiting
- Apply stricter rate limiting to authentication endpoints
- Different limits for login attempts vs. other operations
- IP-based rate limiting for brute force protection

### Input Sanitization
- Sanitize all user inputs to prevent XSS attacks
- Validate email formats and password complexity
- Escape special characters in responses

### Token Security
- Use secure token generation and validation
- Implement proper token expiration handling
- Clear tokens on logout

## Testing Strategy

### Unit Tests
- Test each endpoint with valid and invalid inputs
- Test authentication middleware integration
- Test error handling and response formatting
- Test PocketBase service integration
- Mock external dependencies

### Integration Tests
- Test complete authentication flows
- Test token refresh mechanism
- Test error scenarios
- Verify response format compliance

## Compatibility Notes

### Frontend Compatibility
- Maintain compatibility with existing `ui/stores/auth.js`
- Ensure response formats match frontend expectations
- Support existing authentication flow patterns

### Backend Compatibility
- Use existing error handling patterns
- Follow existing code structure and conventions
- Maintain compatibility with existing middleware
