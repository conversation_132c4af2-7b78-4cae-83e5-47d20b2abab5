# Installation Guide: Track Tasks Application on Ubuntu Server

This document outlines the steps required to deploy and run the Track Tasks web application as a `systemd` service on an Ubuntu server.

## Prerequisites

Before you begin, ensure your Ubuntu server meets the following requirements:

*   **SSH Access:** You must have SSH access to your Ubuntu server.
*   **Sudo Privileges:** You need a user account with `sudo` privileges.
*   **Node.js and npm:** These will be installed as part of the process if not already present.

## Deployment Steps

Follow these steps to install and configure the Track Tasks application:

### Step 1: Transfer the Application Archive

First, transfer the `track-tasks-app.tar.gz` archive (created by the `deploy.sh` script) from your local machine to your Ubuntu server.

```bash
scp track-tasks-app.tar.gz user@your_server_ip:/tmp/
```

Replace `user` with your SSH username and `your_server_ip` with the IP address or hostname of your Ubuntu server.

### Step 2: Log in to Your Server

Connect to your Ubuntu server via SSH:

```bash
ssh user@your_server_ip
```

### Step 3: Extract the Application Archive

Create the target directory and extract the contents of the archive into it:

```bash
sudo mkdir -p /opt/track-tasks
sudo tar -xzvf /tmp/track-tasks-app.tar.gz -C /opt/track-tasks/
```

### Step 4: Install Node.js and npm (if not already installed)

If Node.js and npm are not already installed on your server, install them using `apt`:

```bash
sudo apt update
sudo apt install nodejs npm
```

### Step 5: Install Production Dependencies

Navigate to the application directory and install only the necessary production dependencies. This will install `express` and any other runtime dependencies.

```bash
cd /opt/track-tasks
npm install --production
```

### Step 6: Configure Environment Variables

Create a `.env` file in the application's root directory (`/opt/track-tasks`) to store your PocketBase connection details. This file is crucial for the application to connect to your backend.

```bash
sudo nano /opt/track-tasks/.env
```

Add the following content to the `.env` file, replacing the placeholder values with your actual PocketBase URL, superuser email, and password:

```
POCKETBASE_URL=https://srv.erly.co.za
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your_password
```

Save the file (Ctrl+S or Cmd+S) and exit the editor (Ctrl+X or Cmd+X).

### Step 7: Move the `systemd` Service File

The `track-tasks.service` file defines how your application will run as a background service. Move it to the `systemd` configuration directory:

```bash
sudo mv /opt/track-tasks/track-tasks.service /etc/systemd/system/
```

### Step 8: Reload `systemd` and Start the Service

Inform `systemd` about the new service file, enable it to start on boot, and then start the service:

```bash
sudo systemctl daemon-reload
sudo systemctl enable track-tasks.service
sudo systemctl start track-tasks.service
```

### Step 9: Check Service Status

Verify that the application service is running correctly:

```bash
sudo systemctl status track-tasks.service
```

You should see output indicating that the service is `active (running)`. If there are errors, check the logs using `journalctl -u track-tasks.service`.

## Important Notes

*   **Port Configuration:** By default, the application server runs on port `3000`. If you need to change this, you can set the `PORT` environment variable in the `track-tasks.service` file (e.g., `Environment=PORT=8080`).
*   **Reverse Proxy:** For production environments, it is highly recommended to set up a reverse proxy (such as Nginx or Apache) in front of your Node.js application. This allows you to serve the application on standard HTTP/HTTPS ports (80/443), handle SSL/TLS encryption, and manage domain names.
*   **Security:** Ensure your PocketBase instance is properly secured and accessible only from trusted sources if it's not publicly exposed.
