# UI to API Migration Requirements

## Executive Summary

This document outlines the requirements for migrating the UI layer from direct PocketBase service usage to API-based communication. This migration addresses the runtime error where `import.meta.env.VITE_POCKETBASE_URL` is undefined in Node.js server environments and establishes a proper client-server architecture.

## 1. Analysis Phase

### 1.1 Current PocketBase Service Usage Audit

#### Primary Usage Location
- **File**: `ui/stores/auth.js` (Lines 2, 44, 77, 100, 140, 143, 144, 175, 203, 226, 241, 260, 262, 287, 289)
- **Import**: `import { pocketBaseService } from '../../common/services/pocketbase.js'`

#### Methods Currently Used by UI
1. **Authentication Methods**:
   - `pocketBaseService().login(email, password)` - Line 44
   - `pocketBaseService().logout()` - Line 77
   - `pocketBaseService().register(userData)` - Line 175
   - `pocketBaseService().refreshToken()` - Line 100

2. **User Management Methods**:
   - `pocketBaseService().getCurrentUser()` - Line 143
   - `pocketBaseService().getToken()` - Line 144
   - `pocketBaseService().isAuthenticated()` - Line 140
   - `pocketBaseService().updateProfile(userData)` - Line 203

3. **Password Management Methods**:
   - `pocketBaseService().requestPasswordReset(email)` - Line 226
   - `pocketBaseService().confirmPasswordReset(token, password)` - Line 241
   - `pocketBaseService().resetPassword(tokenOrData)` - Lines 260, 262
   - `pocketBaseService().changePassword(currentPasswordOrData, newPassword)` - Lines 287, 289

#### Other UI Components
- **Tasks Store**: `ui/stores/tasks.js` - Uses `getDatabaseService()` (no PocketBase direct usage)
- **Projects Store**: `ui/stores/projects.js` - Uses `getDatabaseService()` (no PocketBase direct usage)
- **Session Management**: `ui/composables/useSessionManagement.js` - Already uses API endpoints
- **Session Timeout**: `ui/composables/useSessionTimeout.js` - Already uses API endpoints

### 1.2 Test Files Using PocketBase Service
- `ui/stores/__tests__/auth.spec.js` - Line 8: Mock import for testing
- `test/unit/composables/useAuth.test.js` - Uses mocked auth store
- `test/integration/auth-flows.test.js` - Line 27: Mock import for testing

## 2. API Endpoint Gap Analysis

### 2.1 Existing API Endpoints (Available)

#### Authentication Endpoints (`/api/auth/`)
✅ **POST** `/login` - User login with email and password  
✅ **POST** `/register` - User registration  
✅ **POST** `/logout` - User logout  
✅ **POST** `/refresh` - Token refresh  
✅ **GET** `/me` - Get current user profile  
✅ **PUT** `/profile` - Update user profile  
✅ **PUT** `/password` - Change user password  
✅ **POST** `/forgot-password` - Request password reset  
✅ **POST** `/reset-password` - Reset password with token  

#### Session Management Endpoints (`/api/auth/`)
✅ **GET** `/sessions` - List user sessions  
✅ **DELETE** `/sessions/:sessionId` - Terminate specific session  
✅ **DELETE** `/sessions` - Terminate all other sessions  
✅ **POST** `/extend-session` - Extend current session  
✅ **GET** `/session-status` - Get session status  

#### Task Management Endpoints (`/api/tasks/`)
✅ **POST** `/` - Create new task  
✅ **GET** `/` - List all tasks with filtering  
✅ **GET** `/:task_id` - Get specific task  
✅ **PUT** `/:task_id` - Update task  
✅ **DELETE** `/:task_id` - Delete task  
✅ **PATCH** `/:task_id/status` - Update task status  

#### Project Management Endpoints (`/api/projects/`)
✅ **POST** `/` - Create new project  
✅ **GET** `/` - List all projects  
✅ **GET** `/:project_id` - Get specific project  
✅ **PUT** `/:project_id` - Update project  
✅ **DELETE** `/:project_id` - Delete project  
✅ **GET** `/:project_id/tasks` - Get project tasks  

### 2.2 Missing API Endpoints

❌ **No missing endpoints identified** - All required functionality is already available through existing API endpoints.

## 3. Migration Requirements

### 3.1 Auth Store Refactoring (`ui/stores/auth.js`)

#### Required Changes
1. **Remove PocketBase Import**:
   - Remove: `import { pocketBaseService } from '../../common/services/pocketbase.js'`
   - Add: HTTP client utility for API calls

2. **Method Mapping**:
   - `pocketBaseService().login()` → `POST /api/auth/login`
   - `pocketBaseService().logout()` → `POST /api/auth/logout`
   - `pocketBaseService().register()` → `POST /api/auth/register`
   - `pocketBaseService().refreshToken()` → `POST /api/auth/refresh`
   - `pocketBaseService().getCurrentUser()` → `GET /api/auth/me`
   - `pocketBaseService().getToken()` → Local storage access
   - `pocketBaseService().isAuthenticated()` → Local token validation
   - `pocketBaseService().updateProfile()` → `PUT /api/auth/profile`
   - `pocketBaseService().requestPasswordReset()` → `POST /api/auth/forgot-password`
   - `pocketBaseService().confirmPasswordReset()` → `POST /api/auth/reset-password`
   - `pocketBaseService().resetPassword()` → `POST /api/auth/reset-password`
   - `pocketBaseService().changePassword()` → `PUT /api/auth/password`

### 3.2 HTTP Client Implementation

#### Requirements
1. **Base HTTP Client**:
   - Create `ui/utils/httpClient.js`
   - Handle authentication headers automatically
   - Implement request/response interceptors
   - Support CSRF token handling

2. **Error Handling**:
   - Standardized error response format
   - Network error handling
   - Authentication error handling (401/403)
   - Rate limiting error handling (429)

3. **Authentication Token Management**:
   - Automatic token attachment to requests
   - Token refresh on 401 responses
   - Secure token storage in localStorage
   - Token validation utilities

### 3.3 Environment Variable Handling

#### Current Issue
- PocketBase service uses `import.meta.env.VITE_POCKETBASE_URL`
- This is undefined in Node.js server environments
- API endpoints use relative URLs (`/api/auth/login`)

#### Solution
- Remove environment variable dependency from UI
- Use relative API URLs for all HTTP requests
- API server handles PocketBase connection internally

## 4. Test Cases

### 4.1 Authentication Flow Tests
1. **Login Flow**:
   - Valid credentials → successful login
   - Invalid credentials → error handling
   - Network error → proper error display
   - Rate limiting → appropriate user feedback

2. **Registration Flow**:
   - Valid registration data → account creation
   - Duplicate email → validation error
   - Password mismatch → validation error
   - Network error → error handling

3. **Token Management**:
   - Token refresh on expiration
   - Automatic logout on invalid token
   - Token persistence across browser sessions
   - Cross-tab session synchronization

### 4.2 Error Handling Tests
1. **Network Errors**:
   - Connection timeout
   - Server unavailable (500 errors)
   - Rate limiting (429 errors)
   - CSRF token validation

2. **Authentication Errors**:
   - Expired token handling
   - Invalid token handling
   - Session timeout handling
   - Concurrent session management

### 4.3 Backward Compatibility Tests
1. **Store Interface**:
   - All existing store methods work unchanged
   - Return values maintain same structure
   - Error handling maintains same format
   - Loading states work correctly

2. **Component Integration**:
   - Existing components work without changes
   - Composables maintain same interface
   - Router guards function correctly
   - Session management works properly

## 5. Implementation Guidelines

### 5.1 Migration Approach

#### Phase 1: HTTP Client Setup
1. Create HTTP client utility
2. Implement authentication interceptors
3. Add CSRF token handling
4. Create error handling utilities

#### Phase 2: Auth Store Migration
1. Replace PocketBase calls with HTTP requests
2. Update token management logic
3. Maintain existing store interface
4. Update error handling

#### Phase 3: Testing & Validation
1. Update unit tests for new HTTP client
2. Update integration tests
3. Validate all authentication flows
4. Performance testing

#### Phase 4: Cleanup
1. Remove PocketBase service dependency from UI
2. Update documentation
3. Remove unused imports
4. Code review and optimization

### 5.2 Rollback Procedures

#### Rollback Strategy
1. **Git Branch Strategy**:
   - Create feature branch for migration
   - Maintain main branch stability
   - Use feature flags if needed

2. **Rollback Steps**:
   - Revert auth store changes
   - Restore PocketBase service imports
   - Update environment variable handling
   - Restore original test mocks

### 5.3 Environment Variable Configuration

#### Development Environment
- API calls use relative URLs (`/api/auth/login`)
- Vite dev server proxies to API server
- No environment variables needed in UI

#### Production Environment
- API calls use relative URLs
- Reverse proxy handles routing
- API server manages PocketBase connection
- UI remains environment-agnostic

## 6. Success Criteria

### 6.1 Functional Requirements
- ✅ All authentication flows work correctly
- ✅ Token management functions properly
- ✅ Error handling maintains user experience
- ✅ Session management works across tabs
- ✅ Password reset flows function correctly

### 6.2 Technical Requirements
- ✅ No PocketBase imports in UI layer
- ✅ No environment variable dependencies in UI
- ✅ All tests pass after migration
- ✅ Performance maintains current levels
- ✅ Code maintainability improves

### 6.3 Deployment Requirements
- ✅ Works in both development and production
- ✅ No breaking changes for existing deployments
- ✅ Rollback capability maintained
- ✅ Documentation updated
- ✅ Team training completed

## 7. Risk Assessment

### 7.1 High Risk Items
1. **Token Management Changes**: Risk of breaking existing sessions
2. **Error Handling**: Risk of degraded user experience
3. **Cross-tab Synchronization**: Risk of session conflicts

### 7.2 Mitigation Strategies
1. **Comprehensive Testing**: Unit, integration, and E2E tests
2. **Gradual Rollout**: Feature flags for controlled deployment
3. **Monitoring**: Enhanced logging and error tracking
4. **Rollback Plan**: Quick revert capability

## 8. Timeline Estimate

- **Phase 1 (HTTP Client)**: 2-3 days
- **Phase 2 (Auth Store Migration)**: 3-4 days
- **Phase 3 (Testing)**: 2-3 days
- **Phase 4 (Cleanup)**: 1-2 days

**Total Estimated Time**: 8-12 days

## 9. Dependencies

### 9.1 External Dependencies
- Existing API endpoints (already available)
- CSRF token implementation (already available)
- Session management (already available)

### 9.2 Internal Dependencies
- Auth store refactoring
- HTTP client implementation
- Test suite updates
- Documentation updates

---

**Document Version**: 1.0  
**Last Updated**: 2024-07-24  
**Author**: Product Owner  
**Status**: Ready for Implementation
