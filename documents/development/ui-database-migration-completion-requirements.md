# UI Database Migration Completion Requirements

## Executive Summary

This document provides comprehensive technical requirements for completing the PocketBase to API migration in the Track Tasks application. Based on the analysis findings, while the authentication store has been successfully migrated to use API endpoints, critical components in the UI layer still maintain direct PocketBase dependencies, creating a hybrid architecture that violates separation of concerns.

**Migration Status**: 🔴 **INCOMPLETE** - Critical data operations still bypass API layer

**Scope**: Complete elimination of direct PocketBase usage from `/ui` directory while maintaining identical functionality through API endpoints.

## 1. Analysis Summary & Context

### 1.1 Current State Assessment

Based on the comprehensive analysis conducted, the following components require migration:

#### ✅ Successfully Migrated (Reference Implementation)
- **Auth Store** (`ui/stores/auth.js`): Uses `httpClient` for all authentication flows
- **HTTP Client** (`ui/utils/httpClient.js`): Properly implemented with error handling and retry logic

#### ❌ Requires Migration (Critical Issues)
- **Application Bootstrap** (`ui/main.js:29`): Direct PocketBase admin authentication
- **Tasks Store** (`ui/stores/tasks.js:10`): All CRUD operations bypass API endpoints
- **Projects Store** (`ui/stores/projects.js:7`): All project operations bypass API endpoints
- **Database Initializer** (`ui/services/databaseInitializer.js:5`): Schema operations in browser
- **Migration Utilities** (`ui/utils/migration.js:4`): Data migration runs client-side

### 1.2 Root Cause Analysis

**Primary Issue**: The network request to `https://srv.erly.co.za/api/collections/_superusers/auth-with-password` originates from:
- **File**: `ui/main.js`
- **Line**: 29
- **Code**: `await databaseService.authenticate();`

**Secondary Issues**:
- Tasks and Projects stores import `getDatabaseService()` directly
- Database initialization and migration utilities run in browser environment
- Environment variables `VITE_POCKETBASE_*` still accessible to UI layer

### 1.3 Architecture Goals

**Current (Incorrect) Architecture:**
```
UI Layer ──(Direct PocketBase)──► PocketBase Database
    │
    └──(HTTP/REST - Partial)──► API Layer ──► PocketBase Database
```

**Target (Correct) Architecture:**
```
UI Layer ──(HTTP/REST Only)──► API Layer ──(PocketBase Only)──► PocketBase Database
```

## 2. Migration Requirements by Component

### 2.1 Application Bootstrap Migration (`ui/main.js`)

#### 2.1.1 Current Implementation Issues
```javascript
// PROBLEMATIC CODE - Lines 20, 24, 29
import { getDatabaseService } from '@common/services/databaseService'
const databaseService = getDatabaseService()
await databaseService.authenticate(); // ← DIRECT POCKETBASE CALL
```

#### 2.1.2 Required Changes

**Remove Direct Database Dependencies:**
- **Line 20**: Remove `import { getDatabaseService } from '@common/services/databaseService'`
- **Line 24**: Remove `const databaseService = getDatabaseService()`
- **Line 29**: Remove `await databaseService.authenticate();`

**Replace with API-based Initialization:**
- Remove database authentication from application bootstrap
- Implement user-driven authentication through login page
- Move database initialization to API layer endpoints

#### 2.1.3 Implementation Approach

**Step 1: Remove Direct Authentication**
```javascript
// REMOVE THESE LINES:
// import { getDatabaseService } from '@common/services/databaseService'
// const databaseService = getDatabaseService()
// await databaseService.authenticate();

// REPLACE WITH:
// Application starts without database authentication
// Users authenticate through login page using existing auth store
```

**Step 2: Update Application Flow**
- Application starts immediately without database connection
- Authentication handled through user login (existing auth store)
- Database operations only occur after user authentication
- Remove database initialization from startup (move to API layer)

**Step 3: Error Handling Update**
```javascript
// UPDATE ERROR HANDLING:
// Remove database connection error handling
// Keep general application startup error handling
// Add user-friendly messages for unauthenticated state
```

### 2.2 Tasks Store Migration (`ui/stores/tasks.js`)

#### 2.2.1 Current Implementation Issues
```javascript
// PROBLEMATIC CODE - Line 10
import { getDatabaseService } from '../../common/services/databaseService.js'

// PROBLEMATIC USAGE - Lines: 138, 178, 219, 254, 306, 333, 355, 368, 382
const fetchedTasks = await getDatabaseService().getAllTasks(filters)
```

#### 2.2.2 Required API Endpoints

**Existing Endpoints (Verified Available):**
- `GET /api/tasks` - Fetch tasks with filtering
- `GET /api/tasks/:task_id` - Get single task
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/:task_id` - Update task
- `DELETE /api/tasks/:task_id` - Delete task
- `PATCH /api/tasks/:task_id/status` - Update task status

**Additional Endpoints Required:**
- `POST /api/tasks/bulk` - Bulk task import from JSON
- `DELETE /api/tasks/all` - Clear all tasks (admin operation)
- `GET /api/projects/:project_id/tasks` - Get tasks by project

#### 2.2.3 Method-by-Method Migration Requirements

**Method: `fetchTasks()` (Line 138)**
```javascript
// CURRENT:
const fetchedTasks = await getDatabaseService().getAllTasks(filters)

// MIGRATE TO:
const response = await httpClient.get('/tasks', { params: filters })
const fetchedTasks = response.data.tasks || []
```

**Method: `insertTasksFromJson()` (Line 178)**
```javascript
// CURRENT:
const result = await getDatabaseService().insertTasksFromJson(jsonData, projectId)

// MIGRATE TO:
const response = await httpClient.post('/tasks/bulk', {
  tasks: jsonData,
  project_id: projectId
})
const result = response.data
```

**Method: `updateTaskStatus()` (Line 219)**
```javascript
// CURRENT:
const success = await getDatabaseService().updateTaskStatus(taskToUpdate.id, newStatus)

// MIGRATE TO:
const response = await httpClient.patch(`/tasks/${taskToUpdate.id}/status`, {
  status: newStatus
})
const success = response.success
```

**Method: `updateTask()` (Line 254)**
```javascript
// CURRENT:
const updatedTask = await getDatabaseService().updateTask(taskToUpdate.id, taskData)

// MIGRATE TO:
const response = await httpClient.put(`/tasks/${taskToUpdate.id}`, taskData)
const updatedTask = response.data.task
```

**Method: `addTask()` (Line 306)**
```javascript
// CURRENT:
const createdTask = await getDatabaseService().addTask(newTaskData)

// MIGRATE TO:
const response = await httpClient.post('/tasks', newTaskData)
const createdTask = response.data.task
```

**Method: `deleteTask()` (Line 333)**
```javascript
// CURRENT:
const success = await getDatabaseService().deleteTask(taskToDelete.id)

// MIGRATE TO:
const response = await httpClient.delete(`/tasks/${taskToDelete.id}`)
const success = response.success
```

**Method: `getTaskByOriginalId()` (Line 355)**
```javascript
// CURRENT:
return await getDatabaseService().getTaskByOriginalId(taskId)

// MIGRATE TO:
const response = await httpClient.get(`/tasks/${taskId}`)
return response.data.task
```

**Method: `clearAllTasks()` (Line 368)**
```javascript
// CURRENT:
const deletedCount = await getDatabaseService().clearAllTasks()

// MIGRATE TO:
const response = await httpClient.delete('/tasks/all')
const deletedCount = response.data.deletedCount
```

**Method: `getTasksByProject()` (Line 382)**
```javascript
// CURRENT:
const projectTasks = await getDatabaseService().getTasksByProject(projectId, filters)

// MIGRATE TO:
const response = await httpClient.get(`/projects/${projectId}/tasks`, { params: filters })
const projectTasks = response.data.tasks || []
```

#### 2.2.4 Import Changes Required
```javascript
// REMOVE:
import { getDatabaseService } from '../../common/services/databaseService.js'

// ADD:
import httpClient from '../utils/httpClient.js'
```

#### 2.2.5 Error Handling Requirements
- Use existing HTTP client error handling patterns from auth store
- Transform API errors to user-friendly messages
- Maintain existing error state management in store
- Handle network failures with retry logic (already implemented in httpClient)

### 2.3 Projects Store Migration (`ui/stores/projects.js`)

#### 2.3.1 Current Implementation Issues
```javascript
// PROBLEMATIC CODE - Lines 7, 10
import getDatabaseService from '@common/services/databaseService'
const databaseService = getDatabaseService()

// PROBLEMATIC USAGE - Lines: 48, 83, 116, 155, 186
const fetchedProjects = await databaseService.getAllProjects(filters)
```

#### 2.3.2 Required API Endpoints

**Existing Endpoints (Verified Available):**
- `GET /api/projects` - Fetch projects with filtering
- `GET /api/projects/:project_id` - Get single project
- `POST /api/projects` - Create new project
- `PUT /api/projects/:project_id` - Update project
- `DELETE /api/projects/:project_id` - Delete project
- `GET /api/projects/:project_id/tasks` - Get tasks by project

#### 2.3.3 Method-by-Method Migration Requirements

**Method: `fetchProjects()` (Line 48)**
```javascript
// CURRENT:
const fetchedProjects = await databaseService.getAllProjects(filters)

// MIGRATE TO:
const response = await httpClient.get('/projects', { params: filters })
const fetchedProjects = response.data.projects || []
```

**Method: `addProject()` (Line 83)**
```javascript
// CURRENT:
const createdProject = await databaseService.addProject(newProjectData)

// MIGRATE TO:
const response = await httpClient.post('/projects', newProjectData)
const createdProject = response.data.project
```

**Method: `updateProject()` (Line 116)**
```javascript
// CURRENT:
const updatedProject = await databaseService.updateProject(projectId, projectData)

// MIGRATE TO:
const response = await httpClient.put(`/projects/${projectId}`, projectData)
const updatedProject = response.data.project
```

**Method: `deleteProject()` (Line 155)**
```javascript
// CURRENT:
const success = await databaseService.deleteProject(projectId)

// MIGRATE TO:
const response = await httpClient.delete(`/projects/${projectId}`)
const success = response.success
```

**Method: `getTasksByProject()` (Line 186)**
```javascript
// CURRENT:
const tasks = await databaseService.getTasksByProject(projectId, filters)

// MIGRATE TO:
const response = await httpClient.get(`/projects/${projectId}/tasks`, { params: filters })
const tasks = response.data.tasks || []
```

#### 2.3.4 Import Changes Required
```javascript
// REMOVE:
import getDatabaseService from '@common/services/databaseService'
const databaseService = getDatabaseService()

// ADD:
import httpClient from '../utils/httpClient.js'
```

### 2.4 Database Initializer Migration (`ui/services/databaseInitializer.js`)

#### 2.4.1 Current Implementation Issues
```javascript
// PROBLEMATIC CODE - Lines 5, 8
import getDatabaseService from '@common/services/databaseService.js'
const databaseService = getDatabaseService()

// PROBLEMATIC USAGE - Lines: 46, 56, 74, 93, 110, 130
const projects = await databaseService.getAllProjects()
```

#### 2.4.2 Migration Strategy

**Option A: Remove from UI Layer (Recommended)**
- Delete `ui/services/databaseInitializer.js` entirely
- Move initialization logic to API layer
- Create API endpoints for initialization if needed

**Option B: Convert to API Calls**
- Replace database service calls with HTTP client calls
- Use existing project and task API endpoints
- Maintain initialization logic in UI layer

#### 2.4.3 Recommended Implementation (Option A)

**Step 1: Create API Initialization Endpoints**
```javascript
// NEW API ENDPOINTS REQUIRED:
POST /api/admin/initialize - Initialize database schema and default data
GET /api/admin/status - Check initialization status
```

**Step 2: Remove UI Initializer**
- Delete `ui/services/databaseInitializer.js`
- Remove import from `ui/main.js:21`
- Remove initialization call from `ui/main.js:32`

**Step 3: Update Application Flow**
- Remove initialization from application startup
- Handle initialization through admin interface if needed
- Or run initialization automatically on API server startup

#### 2.4.4 Required API Endpoint Specifications

**Endpoint: `POST /api/admin/initialize`**
```json
{
  "method": "POST",
  "path": "/api/admin/initialize",
  "description": "Initialize database schema and create default project",
  "authentication": "Admin required",
  "request_body": {},
  "response_success": {
    "success": true,
    "data": {
      "initialized": true,
      "default_project_created": true,
      "migrations_run": ["migration1", "migration2"]
    },
    "message": "Database initialized successfully"
  },
  "response_error": {
    "success": false,
    "error": {
      "code": "INITIALIZATION_FAILED",
      "message": "Database initialization failed",
      "details": "Specific error details"
    }
  }
}
```

**Endpoint: `GET /api/admin/status`**
```json
{
  "method": "GET",
  "path": "/api/admin/status",
  "description": "Check database initialization status",
  "authentication": "Admin required",
  "response_success": {
    "success": true,
    "data": {
      "initialized": true,
      "default_project_exists": true,
      "schema_version": "1.0.0"
    }
  }
}
```

### 2.5 Migration Utilities Migration (`ui/utils/migration.js`)

#### 2.5.1 Current Implementation Issues
```javascript
// PROBLEMATIC CODE - Line 4
import databaseService from '@/services/databaseService'

// PROBLEMATIC USAGE - Lines: 14, 17, 47, 68, 105, 123, 131, 143, 185, 186
const projects = await databaseService.getAllProjects()
```

#### 2.5.2 Migration Strategy

**Option A: Remove from UI Layer (Recommended)**
- Delete `ui/utils/migration.js` entirely
- Move migration logic to API layer
- Create API endpoints for migration operations

**Option B: Convert to API Calls**
- Replace database service calls with HTTP client calls
- Use existing API endpoints for data operations
- Maintain migration logic in UI layer

#### 2.5.3 Recommended Implementation (Option A)

**Step 1: Create API Migration Endpoints**
```javascript
// NEW API ENDPOINTS REQUIRED:
GET /api/admin/migration/status - Check if migration is needed
POST /api/admin/migration/run - Execute migration
GET /api/admin/migration/progress - Get migration progress
```

**Step 2: Remove UI Migration Utilities**
- Delete `ui/utils/migration.js`
- Remove any imports of migration utilities
- Handle migrations through API layer

#### 2.5.4 Required API Endpoint Specifications

**Endpoint: `GET /api/admin/migration/status`**
```json
{
  "method": "GET",
  "path": "/api/admin/migration/status",
  "description": "Check if data migration is needed",
  "authentication": "Admin required",
  "response_success": {
    "success": true,
    "data": {
      "migration_needed": true,
      "tasks_without_project": 25,
      "epics_to_migrate": 5,
      "estimated_duration": "2 minutes"
    }
  }
}
```

**Endpoint: `POST /api/admin/migration/run`**
```json
{
  "method": "POST",
  "path": "/api/admin/migration/run",
  "description": "Execute data migration",
  "authentication": "Admin required",
  "request_body": {
    "migration_type": "project_assignment|epic_to_project|all"
  },
  "response_success": {
    "success": true,
    "data": {
      "migration_id": "mig_123456",
      "status": "running",
      "progress": 0
    }
  }
}
```

**Endpoint: `GET /api/admin/migration/progress`**
```json
{
  "method": "GET",
  "path": "/api/admin/migration/progress",
  "description": "Get migration progress",
  "authentication": "Admin required",
  "query_params": {
    "migration_id": "mig_123456"
  },
  "response_success": {
    "success": true,
    "data": {
      "migration_id": "mig_123456",
      "status": "completed|running|failed",
      "progress": 100,
      "tasks_migrated": 25,
      "projects_created": 5,
      "errors": []
    }
  }
}
```

## 3. API Endpoint Requirements

### 3.1 Missing API Endpoints

Based on the analysis, the following API endpoints need to be created to support the migration:

#### 3.1.1 Bulk Operations Endpoints

**Endpoint: `POST /api/tasks/bulk`**
```json
{
  "method": "POST",
  "path": "/api/tasks/bulk",
  "description": "Import multiple tasks from JSON data",
  "authentication": "User required",
  "request_body": {
    "tasks": [
      {
        "task_id": "TASK-001",
        "summary": "Task title",
        "description": "Task description",
        "priority": "High",
        "type": "Story",
        "status": "Backlog"
      }
    ],
    "project_id": "project-uuid"
  },
  "response_success": {
    "success": true,
    "data": {
      "imported_count": 25,
      "failed_count": 0,
      "tasks": [
        {
          "id": "task-uuid",
          "task_id": "TASK-001",
          "summary": "Task title"
        }
      ]
    },
    "message": "Tasks imported successfully"
  },
  "response_error": {
    "success": false,
    "error": {
      "code": "BULK_IMPORT_FAILED",
      "message": "Some tasks failed to import",
      "details": {
        "failed_tasks": [
          {
            "task_id": "TASK-002",
            "error": "Duplicate task ID"
          }
        ]
      }
    }
  }
}
```

**Endpoint: `DELETE /api/tasks/all`**
```json
{
  "method": "DELETE",
  "path": "/api/tasks/all",
  "description": "Clear all tasks (admin operation)",
  "authentication": "Admin required",
  "request_body": {
    "confirm": true
  },
  "response_success": {
    "success": true,
    "data": {
      "deleted_count": 150
    },
    "message": "All tasks deleted successfully"
  }
}
```

#### 3.1.2 Admin Operations Endpoints

**Endpoint: `POST /api/admin/initialize`**
- Already specified in section 2.4.4

**Endpoint: `GET /api/admin/status`**
- Already specified in section 2.4.4

**Endpoint: `GET /api/admin/migration/status`**
- Already specified in section 2.5.4

**Endpoint: `POST /api/admin/migration/run`**
- Already specified in section 2.5.4

**Endpoint: `GET /api/admin/migration/progress`**
- Already specified in section 2.5.4

### 3.2 Response Format Standardization

All API endpoints must follow the established response format:

**Success Response:**
```json
{
  "success": true,
  "data": {
    // Endpoint-specific data
  },
  "message": "Operation successful"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details",
    "timestamp": "2023-01-01T00:00:00.000Z"
  }
}
```

### 3.3 Authentication Requirements

- **User Authentication**: Standard endpoints require valid JWT token
- **Admin Authentication**: Admin endpoints require elevated permissions
- **Rate Limiting**: Apply appropriate rate limits for bulk operations
- **CSRF Protection**: Maintain CSRF protection for state-changing operations

## 4. Environment Configuration Changes

### 4.1 Environment Variables to Remove from UI

The following environment variables should be removed from UI access:

```bash
# REMOVE FROM UI ENVIRONMENT:
VITE_POCKETBASE_URL=https://srv.erly.co.za
VITE_POCKETBASE_EMAIL=<EMAIL>
VITE_POCKETBASE_PASSWORD=OtOr3WsfYO1ujA
```

### 4.2 Environment Variables to Retain in API

These variables should remain available only to the API layer:

```bash
# KEEP IN API ENVIRONMENT ONLY:
POCKETBASE_URL=https://srv.erly.co.za
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=OtOr3WsfYO1ujA
```

### 4.3 Configuration Updates Required

**File: `.env`**
- Remove `VITE_` prefixes from PocketBase variables
- Add API-specific configuration if needed

**File: `common/constants/index.js`**
- Update `ENV_KEYS` to remove `VITE_` prefixes for API-only variables
- Keep UI-specific environment variables separate

## 5. Data Transformation Requirements

### 5.1 Request/Response Mapping

#### 5.1.1 Tasks Data Transformation

**Database Service Response → API Response:**
```javascript
// Database Service (Current):
const tasks = await getDatabaseService().getAllTasks(filters)
// Returns: Array of task objects directly

// API Response (Target):
const response = await httpClient.get('/tasks', { params: filters })
// Returns: { success: true, data: { tasks: [...] }, message: "..." }
// Extract: response.data.tasks
```

**Transformation Logic:**
```javascript
// IN STORE METHOD:
try {
  const response = await httpClient.get('/tasks', { params: filters })
  const fetchedTasks = response.data.tasks || []
  // Continue with existing logic...
} catch (error) {
  // Handle HTTP client errors (already standardized)
  this.error = error.message
  throw error
}
```

#### 5.1.2 Projects Data Transformation

**Database Service Response → API Response:**
```javascript
// Database Service (Current):
const projects = await databaseService.getAllProjects(filters)
// Returns: Array of project objects directly

// API Response (Target):
const response = await httpClient.get('/projects', { params: filters })
// Returns: { success: true, data: { projects: [...] }, message: "..." }
// Extract: response.data.projects
```

### 5.2 Error Handling Transformation

#### 5.2.1 Database Service Errors → HTTP Client Errors

**Current Error Handling:**
```javascript
try {
  const result = await getDatabaseService().someMethod()
} catch (error) {
  // Direct database errors - inconsistent format
  this.error = error.message
}
```

**Target Error Handling:**
```javascript
try {
  const response = await httpClient.someMethod()
} catch (error) {
  // Standardized HTTP client errors
  // error.errorType: 'VALIDATION_ERROR', 'NETWORK_ERROR', etc.
  // error.statusCode: 400, 401, 500, etc.
  // error.message: User-friendly message
  this.error = error.message
}
```

#### 5.2.2 Error Type Mapping

| Database Error | HTTP Error Type | Status Code | User Message |
|----------------|-----------------|-------------|--------------|
| Validation Error | `VALIDATION_ERROR` | 400 | Field-specific validation message |
| Not Found | `NOT_FOUND` | 404 | "Resource not found" |
| Duplicate Key | `CONFLICT` | 409 | "Resource already exists" |
| Network Error | `NETWORK_ERROR` | N/A | "Network error. Please try again." |
| Server Error | `SERVER_ERROR` | 500 | "Server error occurred" |

## 6. Testing Requirements

### 6.1 Unit Test Updates

#### 6.1.1 Tasks Store Tests (`ui/stores/__tests__/tasks.spec.js`)

**Current Mock:**
```javascript
vi.mock('../../../common/services/databaseService.js', () => ({
  getDatabaseService: vi.fn(() => mockDatabaseService),
}))
```

**Required Update:**
```javascript
vi.mock('../../utils/httpClient.js', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}))
```

#### 6.1.2 Projects Store Tests

**New Test File Required:** `ui/stores/__tests__/projects.spec.js`
- Mock HTTP client instead of database service
- Test all project store methods
- Verify API endpoint calls with correct parameters
- Test error handling scenarios

#### 6.1.3 Integration Tests

**Update Required:** All integration tests that mock database service
- Replace database service mocks with HTTP client mocks
- Update test assertions to match API response format
- Verify proper error handling

### 6.2 Test Coverage Requirements

- **Unit Tests**: 90% coverage for all migrated store methods
- **Integration Tests**: Cover all API endpoint interactions
- **Error Scenarios**: Test all error types and recovery
- **Edge Cases**: Test network failures, timeouts, invalid responses

### 6.3 Test Data Requirements

**Mock API Responses:**
```javascript
// Success Response Mock:
const mockSuccessResponse = {
  success: true,
  data: {
    tasks: [{ id: '1', task_id: 'TASK-001', summary: 'Test Task' }]
  },
  message: 'Tasks fetched successfully'
}

// Error Response Mock:
const mockErrorResponse = {
  statusCode: 400,
  errorType: 'VALIDATION_ERROR',
  message: 'Invalid task data'
}
```

## 7. Implementation Timeline & Phases

### 7.1 Phase 1: Critical Fix (Immediate - Day 1)

**Priority**: 🔴 **CRITICAL** - Stops direct PocketBase calls

**Tasks:**
1. **Remove Direct Authentication** (`ui/main.js`)
   - Remove lines 20, 24, 29
   - Update application startup flow
   - Test application starts without database connection

**Acceptance Criteria:**
- ✅ No network requests to `https://srv.erly.co.za/api/collections/_superusers/auth-with-password`
- ✅ Application starts successfully
- ✅ Users can access login page
- ✅ Authentication works through existing auth store

**Estimated Effort**: 2-4 hours

### 7.2 Phase 2: Core Data Operations (Sprint 1 - Week 1)

**Priority**: 🟠 **HIGH** - Core functionality migration

**Tasks:**
1. **Create Missing API Endpoints**
   - `POST /api/tasks/bulk` - Bulk task import
   - `DELETE /api/tasks/all` - Clear all tasks
   - Admin endpoints for initialization and migration

2. **Migrate Tasks Store** (`ui/stores/tasks.js`)
   - Replace all `getDatabaseService()` calls with `httpClient` calls
   - Update all 9 methods identified in analysis
   - Update imports and error handling

3. **Migrate Projects Store** (`ui/stores/projects.js`)
   - Replace all `databaseService` calls with `httpClient` calls
   - Update all 5 methods identified in analysis
   - Update imports and error handling

**Acceptance Criteria:**
- ✅ All task operations use API endpoints
- ✅ All project operations use API endpoints
- ✅ No direct database service imports in stores
- ✅ All existing functionality preserved
- ✅ Error handling maintains user experience

**Estimated Effort**: 3-5 days

### 7.3 Phase 3: Initialization & Migration (Sprint 1 - Week 2)

**Priority**: 🟡 **MEDIUM** - Supporting functionality

**Tasks:**
1. **Remove Database Initializer** (`ui/services/databaseInitializer.js`)
   - Delete file entirely
   - Remove imports from `ui/main.js`
   - Move initialization logic to API layer

2. **Remove Migration Utilities** (`ui/utils/migration.js`)
   - Delete file entirely
   - Remove any imports
   - Move migration logic to API layer

3. **Create Admin API Endpoints**
   - Database initialization endpoints
   - Migration status and execution endpoints
   - Admin interface for database operations

**Acceptance Criteria:**
- ✅ No database initialization in UI layer
- ✅ No migration utilities in UI layer
- ✅ Admin operations available through API
- ✅ Database setup handled by API layer

**Estimated Effort**: 2-3 days

### 7.4 Phase 4: Environment & Testing (Sprint 2 - Week 1)

**Priority**: 🟢 **LOW** - Cleanup and validation

**Tasks:**
1. **Environment Configuration Cleanup**
   - Remove `VITE_POCKETBASE_*` variables from UI environment
   - Update configuration files
   - Update documentation

2. **Test Suite Updates**
   - Update all unit tests to mock HTTP client
   - Create missing test files
   - Update integration tests
   - Verify test coverage

3. **Documentation Updates**
   - Update README.md
   - Update API documentation
   - Create migration completion report

**Acceptance Criteria:**
- ✅ No PocketBase environment variables in UI
- ✅ All tests pass with HTTP client mocks
- ✅ Test coverage meets requirements (90%+)
- ✅ Documentation reflects new architecture

**Estimated Effort**: 2-3 days

## 8. Success Criteria & Validation

### 8.1 Technical Success Criteria

#### 8.1.1 Code Analysis Validation
- ✅ **Zero Direct Imports**: No `import` statements for database service in `/ui` directory
- ✅ **Zero Direct Calls**: No `getDatabaseService()` or `databaseService` calls in UI layer
- ✅ **Zero Environment Variables**: No `VITE_POCKETBASE_*` usage in UI layer
- ✅ **HTTP Client Only**: All data operations use `httpClient` from `ui/utils/httpClient.js`

#### 8.1.2 Network Analysis Validation
- ✅ **No Direct PocketBase Requests**: Zero network requests to `srv.erly.co.za` from browser
- ✅ **API Requests Only**: All requests go to `/api/*` endpoints
- ✅ **Proper Authentication**: All requests use JWT tokens, not PocketBase admin auth

#### 8.1.3 Functional Validation
- ✅ **Feature Parity**: All existing functionality works identically
- ✅ **Error Handling**: User experience maintained for all error scenarios
- ✅ **Performance**: Response times equivalent or better than direct calls

### 8.2 Architectural Success Criteria

#### 8.2.1 Separation of Concerns
- ✅ **UI Layer**: Only HTTP client and API endpoint calls
- ✅ **API Layer**: Only PocketBase and business logic
- ✅ **Database Layer**: Only PocketBase operations

#### 8.2.2 Security Validation
- ✅ **No Admin Credentials**: No PocketBase admin credentials in browser
- ✅ **JWT Authentication**: All operations use user JWT tokens
- ✅ **CSRF Protection**: Maintained for all state-changing operations

#### 8.2.3 Maintainability Validation
- ✅ **Single Source of Truth**: Business logic only in API layer
- ✅ **Consistent Error Handling**: Standardized across all operations
- ✅ **Testability**: All UI operations mockable through HTTP client

### 8.3 User Experience Success Criteria

#### 8.3.1 Functional Requirements
- ✅ **Login/Logout**: Authentication flows work identically
- ✅ **Task Management**: All CRUD operations function correctly
- ✅ **Project Management**: All project operations function correctly
- ✅ **Data Import**: Bulk operations work as expected
- ✅ **Error Messages**: User-friendly error messages maintained

#### 8.3.2 Performance Requirements
- ✅ **Response Times**: API calls complete within 2 seconds
- ✅ **Loading States**: Proper loading indicators for all operations
- ✅ **Error Recovery**: Automatic retry for network failures
- ✅ **Offline Handling**: Graceful degradation when offline

### 8.4 Testing Success Criteria

#### 8.4.1 Test Coverage
- ✅ **Unit Tests**: 90%+ coverage for all migrated components
- ✅ **Integration Tests**: All API endpoints tested
- ✅ **Error Scenarios**: All error types covered
- ✅ **Edge Cases**: Network failures, timeouts, invalid responses

#### 8.4.2 Test Quality
- ✅ **Mock Consistency**: All tests use HTTP client mocks
- ✅ **Realistic Data**: Test data matches production scenarios
- ✅ **Error Simulation**: Tests cover all error conditions
- ✅ **Performance Tests**: Response time validation

## 9. Risk Assessment & Mitigation

### 9.1 High Risk Areas

#### 9.1.1 Data Loss Risk
**Risk**: Bulk operations or migration could cause data loss
**Mitigation**:
- Implement comprehensive backup before migration
- Add data validation in API endpoints
- Implement rollback procedures
- Test with production data copies

#### 9.1.2 Downtime Risk
**Risk**: Application unavailable during migration
**Mitigation**:
- Implement feature flags for gradual rollout
- Maintain backward compatibility during transition
- Plan maintenance windows for critical changes
- Implement health checks for API endpoints

#### 9.1.3 Performance Risk
**Risk**: API calls slower than direct database access
**Mitigation**:
- Implement caching in API layer
- Optimize database queries
- Add performance monitoring
- Implement request batching where appropriate

### 9.2 Medium Risk Areas

#### 9.2.1 Integration Risk
**Risk**: New API endpoints not working correctly
**Mitigation**:
- Comprehensive testing of all endpoints
- Gradual rollout with feature flags
- Monitoring and alerting for API errors
- Rollback procedures for each component

#### 9.2.2 User Experience Risk
**Risk**: Changes in error handling or loading states
**Mitigation**:
- Maintain existing error message patterns
- Preserve loading state behavior
- User acceptance testing
- Gradual rollout to monitor feedback

### 9.3 Low Risk Areas

#### 9.3.1 Environment Configuration
**Risk**: Environment variable changes cause issues
**Mitigation**:
- Document all environment changes
- Test in staging environment first
- Maintain backup of original configuration
- Clear deployment procedures

## 10. Rollback Strategy

### 10.1 Component-Level Rollback

Each phase can be rolled back independently:

#### 10.1.1 Phase 1 Rollback (Application Bootstrap)
```javascript
// ROLLBACK: Restore direct authentication
import { getDatabaseService } from '@common/services/databaseService'
const databaseService = getDatabaseService()
await databaseService.authenticate();
```

#### 10.1.2 Phase 2 Rollback (Stores)
```javascript
// ROLLBACK: Restore database service imports
import { getDatabaseService } from '../../common/services/databaseService.js'
// Restore all original method implementations
```

#### 10.1.3 Phase 3 Rollback (Initialization)
- Restore deleted files from version control
- Restore imports in main.js
- Restore initialization calls

### 10.2 Full System Rollback

**Trigger Conditions:**
- Critical functionality broken
- Data integrity issues
- Performance degradation > 50%
- User experience significantly impacted

**Rollback Procedure:**
1. Revert all code changes to previous commit
2. Restore environment variables
3. Restart application
4. Verify functionality
5. Communicate status to stakeholders

### 10.3 Rollback Testing

- Test rollback procedures in staging environment
- Document rollback steps for each phase
- Assign rollback responsibilities
- Define rollback decision criteria

## 11. Conclusion

This comprehensive requirements document provides detailed specifications for completing the PocketBase to API migration in the Track Tasks application. The migration addresses critical architectural issues identified in the analysis while maintaining full functionality and user experience.

**Key Deliverables:**
1. Complete elimination of direct PocketBase usage from UI layer
2. Proper separation of concerns between UI and API layers
3. Standardized error handling and data transformation
4. Comprehensive testing and validation procedures

**Expected Outcomes:**
- ✅ Improved application architecture and maintainability
- ✅ Enhanced security through proper authentication layers
- ✅ Better scalability and future enhancement capabilities
- ✅ Consistent development patterns across the application

**Next Steps:**
1. Review and approve requirements document
2. Assign development resources for each phase
3. Begin Phase 1 implementation (critical fix)
4. Monitor progress against success criteria
5. Execute testing and validation procedures

The successful completion of this migration will establish a robust, scalable architecture that supports the application's future growth and enhancement requirements.