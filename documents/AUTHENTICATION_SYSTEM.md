# Comprehensive Authentication System

This document describes the comprehensive authentication system implemented for the track-tasks application.

## Overview

The authentication system provides complete route protection, ensuring that all application functionality requires proper user authentication while maintaining a seamless user experience through proper redirects and state management.

## Features Implemented

### 1. Route Protection

**Protected Routes (Require Authentication):**
- Root page (`/`)
- Dashboard (`/dashboard`) 
- Projects page (`/projects`)
- Tasks pages (`/tasks/*`)
- Upload page (`/upload`)
- Profile page (`/profile`)
- Settings pages (`/settings`)

**Guest-Only Routes (Redirect if authenticated):**
- Login page (`/login`)
- Registration page (`/register`)
- Password reset pages (`/forgot-password`, `/reset-password`)

**Public Routes (Always accessible):**
- Help page (`/help`)

### 2. Authentication State Management

**Auth Store Features:**
- Persistent authentication state using localStorage
- Token validation on app initialization
- Automatic token refresh on expiration
- Graceful handling of network errors
- Secure token storage and cleanup

**Key Methods:**
- `initializeAuth()` - Validates stored tokens on app startup
- `login()` - Authenticates user and stores tokens
- `logout()` - Clears auth state and redirects to login
- `handleTokenExpiration()` - Handles expired tokens gracefully
- `validateAuthState()` - Validates current authentication

### 3. Login Flow & Redirects

**Login Process:**
1. User attempts to access protected route
2. Router guard redirects to `/login?redirect=/original-path`
3. User authenticates successfully
4. System redirects to originally requested page or dashboard

**Redirect Handling:**
- Preserves original destination in query parameters
- Defaults to dashboard for successful authentication
- Handles complex URLs with query parameters
- Prevents redirect loops

### 4. Session Persistence

**Token Management:**
- Stores authentication tokens in localStorage
- Validates tokens on app initialization
- Attempts token refresh before expiring sessions
- Clears invalid/expired tokens automatically

**Offline Support:**
- Maintains cached auth state during network errors
- Graceful degradation when API is unavailable
- Preserves user session across browser restarts

### 5. Graceful Error Handling

**Edge Cases Handled:**
- Expired tokens → Automatic logout and redirect to login
- Network errors → Maintains cached state, shows warnings
- Direct URL access → Proper authentication checks
- Browser refresh → Validates and restores auth state
- Invalid tokens → Clears state and redirects to login

## Implementation Details

### Router Configuration

```javascript
// ui/router/index.js
const guestOnlyRoutes = ['/login', '/register', '/forgot-password', '/reset-password']
const publicRoutes = ['/help']

// All other routes require authentication by default
const requiresAuth = !isGuestOnlyRoute && !isPublicRoute
```

### Authentication Guard

```javascript
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Initialize auth if needed
  if (!authStore.initialized) {
    await authStore.initializeAuth()
  }
  
  // Route protection logic
  if (!isAuthenticated && requiresAuth) {
    next({ path: '/login', query: { redirect: to.fullPath } })
    return
  }
  
  if (isAuthenticated && isGuestOnlyRoute) {
    const redirectTo = to.query.redirect || '/dashboard'
    next(redirectTo)
    return
  }
  
  next()
})
```

### HTTP Client Integration

```javascript
// Automatic token handling
this.addRequestInterceptor(async (config) => {
  const token = this.getAuthToken()
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
})

// Automatic logout on 401 errors
this.addResponseInterceptor(
  (response) => response,
  async (error) => {
    if (error.statusCode === 401) {
      this.handleTokenExpiration()
    }
    throw error
  }
)
```

## Security Features

### Token Security
- Secure storage in localStorage (not sessionStorage for persistence)
- Automatic cleanup on logout
- Token validation on every app initialization
- Refresh token support for extended sessions

### Route Security
- Default-deny approach (all routes protected unless explicitly public)
- Comprehensive route coverage
- Prevention of unauthorized access to any application functionality
- Proper handling of direct URL access

### Error Security
- No sensitive information exposed in error messages
- Graceful degradation without security compromise
- Automatic cleanup of invalid authentication state

## Testing

The authentication system includes comprehensive test coverage:

- **35 test cases** covering all authentication scenarios
- **Protected route tests** for all application routes
- **Guest-only route tests** with proper redirects
- **Public route tests** for unrestricted access
- **Authentication initialization tests**
- **Redirect preservation tests** for complex URLs

## Usage Examples

### Accessing Protected Routes
```javascript
// User not authenticated
// GET /projects → Redirect to /login?redirect=/projects

// User authenticated  
// GET /projects → Allow access
```

### Login Flow
```javascript
// User visits /login?redirect=/projects
// After successful login → Redirect to /projects
// No redirect parameter → Redirect to /dashboard
```

### Token Expiration
```javascript
// API returns 401 → Automatic logout
// Redirect to /login?redirect=/current-path
// Preserve user's current location
```

## Configuration

### Environment Variables
- `JWT_SECRET` - Secret key for token validation
- `FRONTEND_URL` - Allowed CORS origin for API requests

### Default Settings
- Default redirect: `/dashboard`
- Token storage: `localStorage`
- Session persistence: Enabled
- Automatic refresh: Enabled

## Maintenance

### Adding New Protected Routes
1. Create the Vue page component
2. Routes are automatically protected (default behavior)
3. No additional configuration needed

### Adding New Public Routes
1. Create the Vue page component  
2. Add route path to `publicRoutes` array in router configuration

### Debugging Authentication Issues
1. Check browser localStorage for `auth_token` and `auth_user`
2. Monitor network requests for 401 responses
3. Check console for authentication initialization logs
4. Verify API server is running and accessible

This comprehensive authentication system ensures that the track-tasks application is secure, user-friendly, and robust against various edge cases and error conditions.
