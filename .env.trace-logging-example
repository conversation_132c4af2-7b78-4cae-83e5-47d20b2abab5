# Example Environment Configuration for Trace Logging
# Copy these settings to your .env file to enable trace logging

# Track Tasks API Configuration
API_PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration (PocketBase)
POCKETBASE_URL=http://localhost:8090
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your-pocketbase-password

# Security Configuration
JWT_SECRET=your-jwt-secret-key-change-in-production
API_KEY=your-api-key-for-programmatic-access

# Development/Testing
DEBUG=true
LOG_LEVEL=info

# Trace Logging Configuration - ENABLE FOR DEBUGGING
ENABLE_TRACE_LOGGING=true
TRACE_LOG_LEVEL=debug
TRACE_LOG_FORMAT=json

# Trace Logging Options:
# ENABLE_TRACE_LOGGING: true/false - Master switch
# TRACE_LOG_LEVEL: error/warn/info/debug/trace - Minimum level to log
# TRACE_LOG_FORMAT: json/text - Output format

# Example configurations for different environments:

# Development (full debugging):
# ENABLE_TRACE_LOGGING=true
# TRACE_LOG_LEVEL=debug
# TRACE_LOG_FORMAT=json

# Production debugging (errors only):
# ENABLE_TRACE_LOGGING=true
# TRACE_LOG_LEVEL=error
# TRACE_LOG_FORMAT=text

# Disabled (production default):
# ENABLE_TRACE_LOGGING=false
