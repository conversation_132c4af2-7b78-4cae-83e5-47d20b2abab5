/**
 * Authentication Service
 * Business logic layer for authentication operations
 * Integrates with PocketBase for user management
 */

import { errors } from '../middleware/errorHandler.js'
import { sanitizeData } from '../../common/validation/index.js'
import { getDatabaseService } from '../../common/services/databaseService.js'
import { SessionService } from './sessionService.js'
import {
  storeResetToken,
  validateResetToken,
  markTokenAsUsed,
  invalidateEmailResetTokens
} from '../middleware/passwordReset.js'
import { traceEntry, traceExit, traceError } from '../utils/traceLogger.js'

// Initialize database service with API configuration

/**
 * Authentication Service Class
 */
export class AuthService {
  /**
   * User login with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {boolean} rememberMe - Remember user session
   * @param {string} userAgent - User agent string
   * @param {string} ip - Client IP address
   * @returns {Promise<Object>} Login result with user data and token
   */
  static async login(email, password, rememberMe = false, userAgent = '', ip = '') {
    const startTime = Date.now()

    // Trace service entry
    traceEntry('AuthService.login', { email, rememberMe }, { userAgent, ip })

    let result = null

    try {
      // Get PocketBase client from database service
      const dbService = await getDatabaseService()
      const pb = dbService.db

      // Authenticate with PocketBase
      const authData = await pb.collection('users').authWithPassword(email, password)

      if (!authData.record) {
        throw errors.UNAUTHORIZED('Invalid credentials')
      }

      // Sanitize user data
      const user = sanitizeData({
        id: authData.record.id,
        email: authData.record.email,
        name: authData.record.name,
        avatar: authData.record.avatar,
        role: authData.record.role || 'user',
        verified: authData.record.verified || false,
        created: authData.record.created,
        updated: authData.record.updated
      })

      // Create session for tracking
      const session = SessionService.createSession(
        user.id,
        authData.token,
        userAgent,
        ip
      )

      result = {
        success: true,
        user,
        token: authData.token,
        refreshToken: authData.token, // PocketBase uses same token for refresh
        sessionId: session.id
      }

      return result

    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.login', error, { duration_ms: duration, ip })
      if (error.status === 400) {
        throw errors.UNAUTHORIZED('Invalid email or password')
      }
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Login failed: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.login', { success: true, userId: result?.user?.id }, duration)
    }
  }

  /**
   * User registration
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration result
   */
  static async register(userData, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.register', { email: userData.email }, { ip, userAgent })
    const startTime = Date.now()

    let result = null

    try {
      const dbService = await getDatabaseService()
      const pb = dbService.db

      // Sanitize input data
      const sanitizedData = sanitizeData({
        name: userData.name,
        email: userData.email,
        password: userData.password,
        passwordConfirm: userData.passwordConfirm,
        role: 'user' // Default role
      })

      // Create user in PocketBase
      const user = await pb.collection('users').create(sanitizedData)

      // Send verification email (PocketBase handles this automatically)
      try {
        await pb.collection('users').requestVerification(user.email)
      } catch (verificationError) {
        // Log but don't fail registration if verification email fails
        console.warn('Failed to send verification email:', verificationError.message)
      }

      result = {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          verified: user.verified || false
        }
      }

      return result

    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.register', error, { duration_ms: duration, ip })
      if (error.status === 400) {
        // Handle validation errors from PocketBase
        const details = error.data?.data || error.message
        throw errors.VALIDATION_ERROR(details)
      }
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Registration failed: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.register', { success: true, userId: result?.user?.id }, duration)
    }
  }

  /**
   * User logout
   * @param {string} token - User token to invalidate
   * @returns {Promise<Object>} Logout result
   */
  static async logout(token, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.logout', { token }, { ip, userAgent })
    const startTime = Date.now()

    try {
      // Invalidate session and blacklist token
      const invalidated = SessionService.invalidateSessionByToken(token)

      if (!invalidated) {
        console.warn(`No session found for token during logout`)
      }

      return {
        success: true,
        message: 'Logout successful'
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.logout', error, { duration_ms: duration, ip })
      throw errors.INTERNAL_SERVER_ERROR(`Logout failed: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.logout', { success: true }, duration)
    }
  }

  /**
   * Refresh authentication token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<Object>} New token data
   */
  static async refreshToken(refreshToken, req = {}) {
    const ip = req.ip || 'unknown'

    traceEntry('AuthService.refreshToken', { refreshToken })
    const startTime = Date.now()
    try {
      const dbService = await getDatabaseService()
      const pb = dbService.db

      // PocketBase automatically handles token refresh
      // We need to validate the current token and return a new one if valid
      if (!pb.authStore.isValid) {
        throw errors.UNAUTHORIZED('Invalid or expired refresh token')
      }

      return {
        success: true,
        token: pb.authStore.token,
        refreshToken: pb.authStore.token
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.refreshToken', error, { duration_ms: duration, ip })
      if (error.code) {
        throw error
      }
      throw errors.UNAUTHORIZED('Token refresh failed')
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.refreshToken', { success: true }, duration)
    }
  }

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise<Object>} Password reset request result
   */
  static async requestPasswordReset(email, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.requestPasswordReset', { email }, { ip, userAgent })
    const startTime = Date.now()

    try {
      const dbService = await getDatabaseService()
      const pb = dbService.db

      // Invalidate any existing reset tokens for this email
      invalidateEmailResetTokens(email)

      // Request password reset from PocketBase
      const resetData = await pb.collection('users').requestPasswordReset(email)

      // If PocketBase provides a token, store it with enhanced security
      if (resetData && resetData.token) {
        storeResetToken(email, resetData.token, 30 * 60 * 1000) // 30 minutes
      }

      return {
        success: true,
        message: 'Password reset link sent to your email'
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.requestPasswordReset', error, { duration_ms: duration, ip })
      // Don't reveal if email exists or not for security
      // But still log the error for monitoring
      console.warn('Password reset request error:', error.message)

      return {
        success: true,
        message: 'Password reset link sent to your email'
      }
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.requestPasswordReset', { success: true }, duration)
    }
  }

  /**
   * Reset password with token
   * @param {string} token - Password reset token
   * @param {string} password - New password
   * @param {string} passwordConfirm - Password confirmation
   * @returns {Promise<Object>} Password reset result
   */
  static async resetPassword(token, password, passwordConfirm, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.resetPassword', { token }, { ip, userAgent })
    const startTime = Date.now()

    try {
      // Validate token with enhanced security
      const tokenValidation = validateResetToken(token)

      if (!tokenValidation.valid) {
        throw errors.UNAUTHORIZED(tokenValidation.reason)
      }

      const dbService = await getDatabaseService()
      const pb = dbService.db

      // Perform password reset with PocketBase
      await pb.collection('users').confirmPasswordReset(token, password, passwordConfirm)

      // Mark token as used (one-time usage enforcement)
      markTokenAsUsed(token)

      // Invalidate all sessions for the user whose password was reset
      // Note: We would need user ID to invalidate sessions, but email-based lookup would be needed
      // const email = tokenValidation.email // Future enhancement

      return {
        success: true,
        message: 'Password reset successful'
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.resetPassword', error, { duration_ms: duration, ip })

      if (error.status === 400) {
        throw errors.VALIDATION_ERROR('Invalid or expired reset token')
      }
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Password reset failed: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.resetPassword', { success: true }, duration)
    }
  }

  /**
   * Get current user profile
   * @param {string} userId - User ID from token
   * @returns {Promise<Object>} User profile data
   */
  static async getCurrentUser(userId, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.getCurrentUser', { userId }, { ip, userAgent })
    const startTime = Date.now()

    try {
      const dbService = await getDatabaseService()
      const pb = dbService.db

      const user = await pb.collection('users').getOne(userId)

      return {
        success: true,
        user: sanitizeData({
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          role: user.role || 'user',
          verified: user.verified || false,
          created: user.created,
          updated: user.updated
        })
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.getCurrentUser', error, { duration_ms: duration, ip })

      if (error.status === 404) {
        throw errors.NOT_FOUND('User')
      }
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Failed to get user profile: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.getCurrentUser', { success: true }, duration)
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated user profile
   */
  static async updateProfile(userId, profileData, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.updateProfile', { userId }, { ip, userAgent })
    const startTime = Date.now()

    try {
      const dbService = await getDatabaseService()
      const pb = dbService.db

      // Sanitize input data
      const sanitizedData = sanitizeData({
        name: profileData.name,
        email: profileData.email,
        avatar: profileData.avatar
      })

      // Remove undefined values
      Object.keys(sanitizedData).forEach(key => {
        if (sanitizedData[key] === undefined) {
          delete sanitizedData[key]
        }
      })

      const user = await pb.collection('users').update(userId, sanitizedData)

      return {
        success: true,
        user: sanitizeData({
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          role: user.role || 'user',
          verified: user.verified || false
        })
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.updateProfile', error, { duration_ms: duration, ip })

      if (error.status === 404) {
        throw errors.NOT_FOUND('User')
      }
      if (error.status === 400) {
        const details = error.data?.data || error.message
        throw errors.VALIDATION_ERROR(details)
      }
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Profile update failed: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.updateProfile', { success: true }, duration)
    }
  }

  /**
   * Change user password
   * @param {string} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @param {string} newPasswordConfirm - New password confirmation
   * @returns {Promise<Object>} Password change result
   */
  static async changePassword(userId, currentPassword, newPassword, newPasswordConfirm, req = {}) {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers?.['user-agent'] || 'unknown'

    traceEntry('AuthService.changePassword', { userId }, { ip, userAgent })
    const startTime = Date.now()

    try {
      const dbService = await getDatabaseService()
      const pb = dbService.db

      await pb.collection('users').update(userId, {
        oldPassword: currentPassword,
        password: newPassword,
        passwordConfirm: newPasswordConfirm
      })

      return {
        success: true,
        message: 'Password changed successfully'
      }
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthService.changePassword', error, { duration_ms: duration, ip })

      if (error.status === 400) {
        if (error.message.includes('oldPassword')) {
          throw errors.UNAUTHORIZED('Current password is incorrect')
        }
        const details = error.data?.data || error.message
        throw errors.VALIDATION_ERROR(details)
      }
      if (error.status === 404) {
        throw errors.NOT_FOUND('User')
      }
      if (error.code) {
        throw error
      }
      throw errors.INTERNAL_SERVER_ERROR(`Password change failed: ${error.message}`)
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthService.changePassword', { success: true }, duration)
    }
  }
}
