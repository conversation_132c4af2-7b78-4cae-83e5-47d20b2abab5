# Track Tasks API Module

This API module provides programmatic access to the Track Tasks application's task management functionality. It exposes RESTful endpoints for managing tasks and projects, with plans for MCP (Model Context Protocol) server integration.

## Features

- **Task Management**: Full CRUD operations for tasks
- **Project Management**: Full CRUD operations for projects
- **Authentication**: Bearer token authentication
- **Validation**: Comprehensive input validation
- **Error Handling**: Standardized error responses
- **Security**: Rate limiting, CORS, and security headers

## Quick Start

### 1. Install Dependencies

All required dependencies are already installed in the main project.

### 2. Start the API Server

```bash
npm run api:start
```

The API server will start on port 3001 by default. You can configure this using the `API_PORT` environment variable.

### 3. Test the API

```bash
# Test authentication (use 'admin-token' for development)
curl -H "Authorization: Bearer admin-token" http://localhost:3001/api/tasks

# Create a new task
curl -X POST -H "Authorization: Bearer admin-token" \
  -H "Content-Type: application/json" \
  -d '{"summary": "Test task", "project_id": "your-project-id"}' \
  http://localhost:3001/api/tasks
```

## API Endpoints

### Authentication

All endpoints require authentication via Bearer token:

```
Authorization: Bearer <token>
```

For development, use `admin-token` as the token.

### Tasks

#### Create Task
```http
POST /api/tasks
Content-Type: application/json

{
  "summary": "Task summary",
  "project_id": "project-id",
  "description": "Optional description",
  "priority": "Medium",
  "type": "Task",
  "epic": "Epic name"
}
```

#### List Tasks
```http
GET /api/tasks?project_id=project-id&status=Backlog&limit=10&offset=0
```

#### Get Task by ID
```http
GET /api/tasks/TASK-001
```

#### Update Task
```http
PUT /api/tasks/TASK-001
Content-Type: application/json

{
  "summary": "Updated summary",
  "status": "In Progress"
}
```

#### Update Task Status
```http
PATCH /api/tasks/TASK-001/status
Content-Type: application/json

{
  "status": "In Progress"
}
```

#### Delete Task
```http
DELETE /api/tasks/TASK-001
```

### Projects

#### Create Project
```http
POST /api/projects
Content-Type: application/json

{
  "name": "Project Name",
  "description": "Optional description"
}
```

#### List Projects
```http
GET /api/projects
```

#### Get Project by ID
```http
GET /api/projects/project-id
```

#### Update Project
```http
PUT /api/projects/project-id
Content-Type: application/json

{
  "name": "Updated Project Name",
  "description": "Updated description"
}
```

#### Delete Project
```http
DELETE /api/projects/project-id
```

#### Get Project Tasks
```http
GET /api/projects/project-id/tasks?status=Backlog&limit=10
```

## Data Formats

### Task Object
```json
{
  "id": "database-uuid",
  "task_id": "TASK-001",
  "summary": "Task summary",
  "description": "Task description",
  "priority": "Medium",
  "type": "Task",
  "status": "Backlog",
  "estimated_effort": "Medium",
  "epic": "Epic name",
  "assigned_to": "<EMAIL>",
  "project_id": "project-id",
  "created_at": "2025-01-17T10:30:00Z",
  "updated_at": "2025-01-17T10:30:00Z"
}
```

### Project Object
```json
{
  "id": "database-uuid",
  "name": "Project Name",
  "description": "Project description",
  "created_at": "2025-01-17T10:30:00Z",
  "updated_at": "2025-01-17T10:30:00Z"
}
```

### Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      "summary: cannot be empty"
    ],
    "timestamp": "2025-01-17T10:30:00Z",
    "request_id": "req_123456"
  }
}
```

## HTTP Status Codes

- **200**: Success (GET, PUT, PATCH)
- **201**: Created (POST)
- **204**: No Content (DELETE)
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found (resource doesn't exist)
- **409**: Conflict (duplicate resource)
- **422**: Unprocessable Entity (business logic errors)
- **500**: Internal Server Error

## Configuration

The API can be configured using environment variables:

```bash
# API Configuration
API_PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration
POCKETBASE_URL=http://localhost:8090
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your-password

# Security
JWT_SECRET=your-secret-key-change-in-production
```

## Development

### Project Structure

```
api/
├── config/
│   └── server.js          # Server configuration
├── middleware/
│   ├── auth.js            # Authentication middleware
│   └── errorHandler.js    # Error handling middleware
├── routes/
│   ├── tasks.js           # Task routes
│   └── projects.js        # Project routes
├── services/
│   ├── taskService.js     # Task business logic
│   └── projectService.js  # Project business logic
├── utils/
│   └── validation.js      # Validation utilities
├── docs/
│   └── README.md          # This documentation
└── server.js              # API server entry point
```

### Running in Development

```bash
# Start API server with hot reload
npm run api:dev

# Run tests
npm run api:test

# Lint code
npm run lint
```

## Security Considerations

1. **Authentication**: The current implementation uses a simple token system for development. In production, integrate with proper PocketBase authentication.

2. **Rate Limiting**: API endpoints are rate-limited to prevent abuse.

3. **Input Validation**: All inputs are validated and sanitized.

4. **Error Handling**: Sensitive information is not exposed in error messages.

5. **CORS**: Configure CORS properly for your frontend domains.

## Future Enhancements

- [ ] MCP Server Integration
- [ ] Real-time WebSocket updates
- [ ] Advanced search and filtering
- [ ] Bulk operations
- [ ] File import/export
- [ ] API versioning
- [ ] OpenAPI specification
- [ ] Comprehensive test suite

## Integration with Existing Codebase

The API module is designed to work seamlessly with the existing Track Tasks application:

- **Database**: Uses the existing `databaseService.js` for all database operations
- **Authentication**: Integrates with PocketBase authentication system
- **Validation**: Uses existing validation utilities where applicable
- **No Breaking Changes**: The API is additive and doesn't affect existing functionality

## Testing

```bash
# Run all tests
npm test

# Run API-specific tests
npm run test:api

# Run integration tests
npm run test:integration
```

## Contributing

1. Follow the existing code patterns and architecture
2. Add comprehensive tests for new features
3. Update documentation for any API changes
4. Ensure all validation rules are properly implemented
5. Test integration with the existing Vue.js frontend

## Support

For issues and questions related to the API module, please check the main project documentation or create an issue in the project repository.
