/**
 * Authentication Routes
 * Defines route handlers for authentication API operations
 */

import express from 'express'
import rateLimit from 'express-rate-limit'
import { authValidationRules } from '../utils/validation.js'
import { authenticate, optionalAuthenticate } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { AuthService } from '../services/authService.js'
import { csrfProtection, generateCSRFToken, csrfTokenEndpoint } from '../middleware/csrf.js'
import { SessionService } from '../services/sessionService.js'
import {
  passwordResetRateLimit,
  emailResetRateLimit,
  progressiveDelayMiddleware,
  validateResetTokenMiddleware
} from '../middleware/passwordReset.js'
import { traceEntry, traceExit, traceHttpRequest, traceHttpResponse, traceError } from '../utils/traceLogger.js'

// Initialize new Router instance
export const authRoutes = express.Router()

// Rate limiting for authentication endpoints (disabled in test environment)
if (process.env.NODE_ENV !== 'test') {
  const authRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: {
      success: false,
      error: {
        code: 'AUTH_RATE_LIMIT_EXCEEDED',
        message: 'Too many authentication attempts, please try again later.',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for token refresh and logout
      return req.path === '/refresh' || req.path === '/logout'
    }
  })

  // Apply rate limiting to all auth routes
  authRoutes.use(authRateLimit)
}

// Apply CSRF protection to all auth routes
authRoutes.use(csrfProtection)

/**
 * @route GET /api/auth/csrf-token
 * @desc Get CSRF token for authentication requests
 * @access Public
 */
authRoutes.get('/csrf-token', csrfTokenEndpoint)

/**
 * @route POST /api/auth/login
 * @desc User login with email and password
 * @access Public
 */
authRoutes.post(
  '/login',
  authValidationRules.login,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()

    // Trace request entry
    traceHttpRequest('POST', '/api/auth/login', req.headers, req.body, req.requestId)
    traceEntry('AuthRoutes.login', { email: req.body.email, rememberMe: req.body.rememberMe }, { ip: req.ip, userAgent: req.headers['user-agent'] })

    try {
      const { email, password, rememberMe } = req.body
      const userAgent = req.headers['user-agent'] || ''
      const ip = req.ip || req.connection.remoteAddress || ''

      const result = await AuthService.login(email, password, rememberMe, userAgent, ip)

      // Generate CSRF token for authenticated session
      generateCSRFToken(req, res, () => {
        const responseData = {
          success: true,
          data: {
            user: result.user,
            token: result.token,
            refreshToken: result.refreshToken
          },
          message: 'Login successful'
        }

        const duration = Date.now() - startTime
        traceHttpResponse('POST', '/api/auth/login', 200, res.getHeaders(), responseData, duration, req.requestId)

        res.status(200).json(responseData)
      })
    } catch (error) {
      const duration = Date.now() - startTime
      traceError('AuthRoutes.login', error, { duration_ms: duration, ip: req.ip })
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.login', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route POST /api/auth/register
 * @desc User registration
 * @access Public
 */
authRoutes.post(
  '/register',
  authValidationRules.register,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.register', { email: req.body.email, name: req.body.name })

    try {
      const { name, email, password, passwordConfirm } = req.body

      const result = await AuthService.register({
        name,
        email,
        password,
        passwordConfirm
      }, req)

      res.status(201).json({
        success: true,
        data: {
          user: result.user
        },
        message: 'Registration successful. Please check your email to verify your account.'
      })
    } catch (error) {
      traceError('AuthRoutes.register', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.register', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route POST /api/auth/logout
 * @desc User logout
 * @access Private
 */
authRoutes.post(
  '/logout',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.logout', { userId: req.user.id })

    try {
      const token = req.headers.authorization?.replace('Bearer ', '')

      const result = await AuthService.logout(token, req)

      res.status(200).json({
        success: true,
        message: result.message
      })
    } catch (error) {
      traceError('AuthRoutes.logout', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.logout', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route POST /api/auth/refresh
 * @desc Token refresh
 * @access Public
 */
authRoutes.post(
  '/refresh',
  authValidationRules.refresh,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.refresh', { refreshToken: '***' })

    try {
      const { refreshToken } = req.body

      const result = await AuthService.refreshToken(refreshToken, req)

      res.status(200).json({
        success: true,
        data: {
          token: result.token,
          refreshToken: result.refreshToken
        }
      })
    } catch (error) {
      traceError('AuthRoutes.refresh', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.refresh', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route POST /api/auth/forgot-password
 * @desc Password reset request
 * @access Public
 */
authRoutes.post(
  '/forgot-password',
  passwordResetRateLimit,
  emailResetRateLimit,
  progressiveDelayMiddleware,
  authValidationRules.forgotPassword,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.forgotPassword', { email: req.body.email })

    try {
      const { email } = req.body

      const result = await AuthService.requestPasswordReset(email, req)

      res.status(200).json({
        success: true,
        message: result.message
      })
    } catch (error) {
      traceError('AuthRoutes.forgotPassword', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.forgotPassword', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route POST /api/auth/reset-password
 * @desc Password reset completion
 * @access Public
 */
authRoutes.post(
  '/reset-password',
  validateResetTokenMiddleware,
  authValidationRules.resetPassword,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.resetPassword', { token: '***' })

    try {
      const { token, password, passwordConfirm } = req.body

      const result = await AuthService.resetPassword(token, password, passwordConfirm, req)

      res.status(200).json({
        success: true,
        message: result.message
      })
    } catch (error) {
      traceError('AuthRoutes.resetPassword', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.resetPassword', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
authRoutes.get(
  '/me',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.getCurrentUser', { userId: req.user.id })

    try {
      const userId = req.user.id

      const result = await AuthService.getCurrentUser(userId, req)

      res.status(200).json({
        success: true,
        data: {
          user: result.user
        }
      })
    } catch (error) {
      traceError('AuthRoutes.getCurrentUser', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.getCurrentUser', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route PUT /api/auth/profile
 * @desc Update user profile
 * @access Private
 */
authRoutes.put(
  '/profile',
  authenticate,
  authValidationRules.updateProfile,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.updateProfile', { userId: req.user.id, updates: req.body })

    try {
      const userId = req.user.id
      const { name, email, avatar } = req.body

      const result = await AuthService.updateProfile(userId, {
        name,
        email,
        avatar
      }, req)

      res.status(200).json({
        success: true,
        data: {
          user: result.user
        },
        message: 'Profile updated successfully'
      })
    } catch (error) {
      traceError('AuthRoutes.updateProfile', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.updateProfile', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route PUT /api/auth/password
 * @desc Change user password
 * @access Private
 */
authRoutes.put(
  '/password',
  authenticate,
  authValidationRules.changePassword,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.changePassword', { userId: req.user.id })

    try {
      const userId = req.user.id
      const { currentPassword, newPassword, newPasswordConfirm } = req.body

      const result = await AuthService.changePassword(
        userId,
        currentPassword,
        newPassword,
        newPasswordConfirm,
        req
      )

      res.status(200).json({
        success: true,
        message: result.message
      })
    } catch (error) {
      traceError('AuthRoutes.changePassword', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.changePassword', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route GET /api/auth/sessions
 * @desc Get current user's active sessions
 * @access Private
 */
authRoutes.get(
  '/sessions',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.getSessions', { userId: req.user.id })

    try {
      const userId = req.user.id
      const currentToken = req.headers.authorization?.replace('Bearer ', '')

      const sessions = SessionService.getUserSessionDetails(userId)

      // Mark current session
      const sessionsWithCurrent = sessions.map(session => ({
        ...session,
        isCurrent: SessionService.getSessionByToken(currentToken)?.id === session.id
      }))

      res.status(200).json({
        success: true,
        data: {
          sessions: sessionsWithCurrent
        }
      })
    } catch (error) {
      traceError('AuthRoutes.getSessions', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.getSessions', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route DELETE /api/auth/sessions/:sessionId
 * @desc Invalidate a specific session
 * @access Private
 */
authRoutes.delete(
  '/sessions/:sessionId',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.invalidateSession', { userId: req.user.id, sessionId: req.params.sessionId })

    try {
      const { sessionId } = req.params
      const userId = req.user.id

      // Verify session belongs to user
      const session = SessionService.getSession(sessionId)
      if (!session || session.userId !== userId) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Session not found',
            timestamp: new Date().toISOString()
          }
        })
      }

      const invalidated = SessionService.invalidateSession(sessionId)

      res.status(200).json({
        success: true,
        message: invalidated ? 'Session invalidated successfully' : 'Session was already inactive'
      })
    } catch (error) {
      traceError('AuthRoutes.invalidateSession', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.invalidateSession', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route DELETE /api/auth/sessions
 * @desc Invalidate all sessions except current
 * @access Private
 */
authRoutes.delete(
  '/sessions',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.invalidateAllSessions', { userId: req.user.id })

    try {
      const userId = req.user.id
      const currentToken = req.headers.authorization?.replace('Bearer ', '')

      const invalidatedCount = SessionService.invalidateAllUserSessions(userId, currentToken)

      res.status(200).json({
        success: true,
        message: `${invalidatedCount} sessions invalidated successfully`
      })
    } catch (error) {
      traceError('AuthRoutes.invalidateAllSessions', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.invalidateAllSessions', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route POST /api/auth/extend-session
 * @desc Extend current session timeout
 * @access Private
 */
authRoutes.post(
  '/extend-session',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.extendSession', { userId: req.user.id })

    try {
      const token = req.headers.authorization?.replace('Bearer ', '')

      // Update session activity
      const updated = SessionService.updateSessionActivity(token)

      if (!updated) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'SESSION_NOT_FOUND',
            message: 'Session not found or expired',
            timestamp: new Date().toISOString()
          }
        })
      }

      res.status(200).json({
        success: true,
        message: 'Session extended successfully',
        data: {
          extendedAt: new Date().toISOString()
        }
      })
    } catch (error) {
      traceError('AuthRoutes.extendSession', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.extendSession', { duration_ms: duration }, duration)
    }
  })
)

/**
 * @route GET /api/auth/session-status
 * @desc Get current session timeout status
 * @access Private
 */
authRoutes.get(
  '/session-status',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('AuthRoutes.getSessionStatus', { userId: req.user.id })

    try {
      const token = req.headers.authorization?.replace('Bearer ', '')
      const session = SessionService.getSessionByToken(token)

      if (!session) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'SESSION_NOT_FOUND',
            message: 'Session not found',
            timestamp: new Date().toISOString()
          }
        })
      }

      const now = Date.now()
      const sessionAge = now - session.lastActivity.getTime()
      const timeoutDuration = 30 * 60 * 1000 // 30 minutes
      const timeRemaining = Math.max(0, timeoutDuration - sessionAge)

      res.status(200).json({
        success: true,
        data: {
          sessionId: session.id,
          lastActivity: session.lastActivity,
          timeRemaining,
          isNearTimeout: timeRemaining <= 5 * 60 * 1000, // 5 minutes
          expiresAt: new Date(session.lastActivity.getTime() + timeoutDuration)
        }
      })
    } catch (error) {
      traceError('AuthRoutes.getSessionStatus', error)
      throw error
    } finally {
      const duration = Date.now() - startTime
      traceExit('AuthRoutes.getSessionStatus', { duration_ms: duration }, duration)
    }
  })
)
