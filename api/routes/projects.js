/**
 * Project Routes
 * Defines route handlers for project management API operations
 */

import express from 'express'
import { validationRules } from '../utils/validation.js'
import { authenticate, requireAdmin } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { ProjectService } from '../services/projectService.js'
import { traceEntry, traceExit, traceHttpRequest, traceHttpResponse, traceError } from '../utils/traceLogger.js'

// Initialize new Router instance
export const projectRoutes = express.Router()

// Create new project
/**
 * @route POST /api/projects
 */
projectRoutes.post(
  '/',
  authenticate,
  validationRules.createProject,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()

    // Trace request entry
    traceHttpRequest('POST', '/api/projects', req.headers, req.body, req.requestId)
    traceEntry('ProjectRoutes.createProject', req.body, { userId: req.user?.id })

    try {
      const projectData = req.body
      const project = await ProjectService.createProject(projectData)

      const responseData = {
        success: true,
        data: { project },
        message: 'Project created successfully'
      }

      traceHttpResponse('POST', '/api/projects', 201, res.getHeaders(), responseData, Date.now() - startTime, req.requestId)
      res.status(201).json(responseData)
    } catch (error) {
      traceError('ProjectRoutes.createProject', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('ProjectRoutes.createProject', { success: true }, Date.now() - startTime)
    }
  })
)

// List all projects
/**
 * @route GET /api/projects
 */
projectRoutes.get(
  '/',
  authenticate,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('ProjectRoutes.listProjects', req.query, { userId: req.user?.id })

    try {
      const filters = req.query
      const projects = await ProjectService.listProjects(filters)

      res.status(200).json({
        success: true,
        data: { projects },
        message: 'Projects fetched successfully'
      })
    } catch (error) {
      traceError('ProjectRoutes.listProjects', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('ProjectRoutes.listProjects', { success: true }, Date.now() - startTime)
    }
  })
)

// Get specific project by ID
/**
 * @route GET /api/projects/:project_id
 */
projectRoutes.get(
  '/:project_id',
  authenticate,
  validationRules.getProject,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('ProjectRoutes.getProject', { projectId: req.params.project_id }, { userId: req.user?.id })

    try {
      const projectId = req.params.project_id
      const project = await ProjectService.getProjectById(projectId)

      if (!project) {
        return res.status(404).json({ error: 'Project not found' })
      }

      res.status(200).json({
        success: true,
        data: { project },
        message: 'Project fetched successfully'
      })
    } catch (error) {
      traceError('ProjectRoutes.getProject', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('ProjectRoutes.getProject', { success: true }, Date.now() - startTime)
    }
  })
)

// Update existing project
/**
 * @route PUT /api/projects/:project_id
 */
projectRoutes.put(
  '/:project_id',
  authenticate,
  validationRules.updateProject,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('ProjectRoutes.updateProject', { projectId: req.params.project_id, updates: req.body }, { userId: req.user?.id })

    try {
      const projectId = req.params.project_id
      const updates = req.body
      const updatedProject = await ProjectService.updateProject(projectId, updates)

      if (!updatedProject) {
        return res.status(404).json({ error: 'Project not updated.' })
      }

      res.status(200).json({
        success: true,
        data: { project: updatedProject },
        message: 'Project updated successfully'
      })
    } catch (error) {
      traceError('ProjectRoutes.updateProject', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('ProjectRoutes.updateProject', { success: true }, Date.now() - startTime)
    }
  })
)

// Delete project
/**
 * @route DELETE /api/projects/:project_id
 */
projectRoutes.delete(
  '/:project_id',
  authenticate,
  requireAdmin,
  validationRules.deleteProject,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('ProjectRoutes.deleteProject', { projectId: req.params.project_id }, { userId: req.user?.id })

    try {
      const projectId = req.params.project_id
      const success = await ProjectService.deleteProject(projectId)

      if (!success) {
        return res.status(404).json({ error: 'Project not deleted.' })
      }

      res.status(200).json({
        success: true,
        message: 'Project deleted successfully'
      })
    } catch (error) {
      traceError('ProjectRoutes.deleteProject', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('ProjectRoutes.deleteProject', { success: true }, Date.now() - startTime)
    }
  })
)

// Get tasks for specific project
/**
 * @route GET /api/projects/:project_id/tasks
 */
projectRoutes.get(
  '/:project_id/tasks',
  authenticate,
  validationRules.getProjectTasks,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('ProjectRoutes.getProjectTasks', { projectId: req.params.project_id, filters: req.query }, { userId: req.user?.id })

    try {
      const projectId = req.params.project_id
      const filters = req.query
      const tasks = await ProjectService.getProjectTasks(projectId, filters)

      res.status(200).json({
        success: true,
        data: { tasks },
        message: 'Project tasks fetched successfully'
      })
    } catch (error) {
      traceError('ProjectRoutes.getProjectTasks', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('ProjectRoutes.getProjectTasks', { success: true }, Date.now() - startTime)
    }
  })
)
