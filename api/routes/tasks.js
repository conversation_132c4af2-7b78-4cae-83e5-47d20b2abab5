/**
 * Task Routes
 * Defines route handlers for task management API operations
 */

import express from 'express'
import { validationRules } from '../utils/validation.js'
import { authenticate, requireAdmin } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { TaskService } from '../services/taskService.js'
import { traceEntry, traceExit, traceHttpRequest, traceHttpResponse, traceError } from '../utils/traceLogger.js'

// Initialize new Router instance
export const taskRoutes = express.Router()

// Create new task
/**
 * @route POST /api/tasks
 */
taskRoutes.post(
  '/',
  authenticate,
  validationRules.createTask,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()

    // Trace request entry
    traceHttpRequest('POST', '/api/tasks', req.headers, req.body, req.requestId)
    traceEntry('TaskRoutes.createTask', req.body, { userId: req.user?.id })

    try {
      const taskData = req.body
      const task = await TaskService.createTask(taskData)

      const responseData = {
        success: true,
        data: { task },
        message: 'Task created successfully'
      }

      traceHttpResponse('POST', '/api/tasks', 201, res.getHeaders(), responseData, Date.now() - startTime, req.requestId)
      res.status(201).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.createTask', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.createTask', { success: true }, Date.now() - startTime)
    }
  })
)

// List all tasks with optional filtering
/**
 * @route GET /api/tasks
 */
taskRoutes.get(
  '/',
  authenticate,
  validationRules.getTasks,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.listTasks', req.query, { userId: req.user?.id })

    try {
      const filters = req.query
      const tasks = await TaskService.listTasks(filters)

      const responseData = {
        success: true,
        data: { tasks },
        message: 'Tasks fetched successfully'
      }

      res.status(200).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.listTasks', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.listTasks', { success: true }, Date.now() - startTime)
    }
  })
)

// Get specific task by task ID
/**
 * @route GET /api/tasks/:task_id
 */
taskRoutes.get(
  '/:task_id',
  authenticate,
  validationRules.getTask,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.getTask', { taskId: req.params.task_id }, { userId: req.user?.id })

    try {
      const taskId = req.params.task_id
      const task = await TaskService.getTaskById(taskId)

      if (!task) {
        return res.status(404).json({ error: 'Task not found' })
      }

      const responseData = {
        success: true,
        data: { task },
        message: 'Task fetched successfully'
      }

      res.status(200).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.getTask', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.getTask', { success: true }, Date.now() - startTime)
    }
  })
)

// Update existing task
/**
 * @route PUT /api/tasks/:task_id
 */
taskRoutes.put(
  '/:task_id',
  authenticate,
  validationRules.updateTask,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.updateTask', { taskId: req.params.task_id, updates: req.body }, { userId: req.user?.id })

    try {
      const taskId = req.params.task_id
      const updates = req.body
      const updatedTask = await TaskService.updateTask(taskId, updates)

      if (!updatedTask) {
        return res.status(404).json({ error: 'Task not updated.' })
      }

      const responseData = {
        success: true,
        data: { task: updatedTask },
        message: 'Task updated successfully'
      }

      res.status(200).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.updateTask', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.updateTask', { success: true }, Date.now() - startTime)
    }
  })
)

// Delete task
/**
 * @route DELETE /api/tasks/:task_id
 */
taskRoutes.delete(
  '/:task_id',
  authenticate,
  requireAdmin,
  validationRules.deleteTask,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.deleteTask', { taskId: req.params.task_id }, { userId: req.user?.id })

    try {
      const taskId = req.params.task_id
      const success = await TaskService.deleteTask(taskId)

      if (!success) {
        return res.status(404).json({ error: 'Task not deleted.' })
      }

      const responseData = {
        success: true,
        message: 'Task deleted successfully'
      }

      res.status(200).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.deleteTask', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.deleteTask', { success: true }, Date.now() - startTime)
    }
  })
)

// Update task status only
/**
 * @route PATCH /api/tasks/:task_id/status
 */
taskRoutes.patch(
  '/:task_id/status',
  authenticate,
  validationRules.updateTaskStatus,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.updateTaskStatus', { taskId: req.params.task_id, status: req.body.status }, { userId: req.user?.id })

    try {
      const taskId = req.params.task_id
      const { status } = req.body
      const updatedTask = await TaskService.updateTaskStatus(taskId, status)

      if (!updatedTask) {
        return res.status(404).json({ error: 'Task not found or status not updated.' })
      }

      const responseData = {
        success: true,
        data: { task: updatedTask },
        message: 'Task status updated successfully'
      }

      res.status(200).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.updateTaskStatus', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.updateTaskStatus', { success: true }, Date.now() - startTime)
    }
  })
)

// Bulk import tasks from JSON
/**
 * @route POST /api/tasks/bulk
 */
taskRoutes.post(
  '/bulk',
  authenticate,
  validationRules.bulkImportTasks,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.bulkImportTasks', { taskCount: req.body.tasks.length, projectId: req.body.project_id }, { userId: req.user?.id })

    try {
      const { tasks, project_id } = req.body
      const result = await TaskService.bulkImportTasks(tasks, project_id)
      res.status(201).json(result)
    } catch (error) {
      traceError('TaskRoutes.bulkImportTasks', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.bulkImportTasks', { success: true }, Date.now() - startTime)
    }
  })
)

// Clear all tasks (admin operation)
/**
 * @route DELETE /api/tasks/all
 */
taskRoutes.delete(
  '/all',
  authenticate,
  requireAdmin,
  validationRules.clearAllTasks,
  asyncHandler(async (req, res) => {
    const startTime = Date.now()
    traceEntry('TaskRoutes.clearAllTasks', { confirm: req.body.confirm }, { userId: req.user?.id })

    try {
      const { confirm } = req.body
      if (!confirm) {
        return res.status(400).json({ error: 'Confirmation required to clear all tasks' })
      }

      const deletedCount = await TaskService.clearAllTasks()
      const responseData = {
        success: true,
        data: { deleted_count: deletedCount },
        message: 'All tasks deleted successfully'
      }

      res.status(200).json(responseData)
    } catch (error) {
      traceError('TaskRoutes.clearAllTasks', error, { duration_ms: Date.now() - startTime, userId: req.user?.id })
      throw error
    } finally {
      traceExit('TaskRoutes.clearAllTasks', { success: true }, Date.now() - startTime)
    }
  })
)
