/**
 * Security Middleware
 * Implements HTTPS enforcement and security headers for production
 */

import { errors } from './errorHandler.js'

/**
 * HTTPS enforcement middleware
 * Redirects HTTP requests to HTTPS in production
 */
export const enforceHTTPS = (req, res, next) => {
  // Skip HTTPS enforcement in development and test environments
  if (process.env.NODE_ENV !== 'production') {
    return next()
  }

  // Check if request is already HTTPS
  const isHTTPS = req.secure ||
                  req.headers['x-forwarded-proto'] === 'https' ||
                  req.headers['x-forwarded-ssl'] === 'on'

  if (!isHTTPS) {
    // Redirect to HTTPS
    const httpsUrl = `https://${req.headers.host}${req.url}`
    return res.redirect(301, httpsUrl)
  }

  next()
}

/**
 * Security headers middleware
 * Adds comprehensive security headers to responses
 */
export const securityHeaders = (req, res, next) => {
  // Strict Transport Security (HSTS)
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  }

  // Content Security Policy for API
  res.setHeader('Content-Security-Policy', "default-src 'none'; frame-ancestors 'none'")

  // X-Frame-Options
  res.setHeader('X-Frame-Options', 'DENY')

  // X-Content-Type-Options
  res.setHeader('X-Content-Type-Options', 'nosniff')

  // Referrer Policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')

  // X-XSS-Protection (legacy browsers)
  res.setHeader('X-XSS-Protection', '1; mode=block')

  // Permissions Policy
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')

  // Cross-Origin Policies
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp')
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin')
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin')

  next()
}

/**
 * Secure cookie configuration
 * Configures secure cookie settings for production
 */
export const secureCookies = (req, res, next) => {
  // Override res.cookie to add secure settings in production
  const originalCookie = res.cookie.bind(res)

  res.cookie = function(name, value, options = {}) {
    if (process.env.NODE_ENV === 'production') {
      options.secure = true
      options.httpOnly = options.httpOnly !== false // Default to httpOnly unless explicitly set to false
      options.sameSite = options.sameSite || 'strict'
    }

    return originalCookie(name, value, options)
  }

  next()
}

/**
 * Authentication-specific security middleware
 * Additional security measures for authentication endpoints
 */
export const authSecurity = (req, res, next) => {
  // Add cache control headers for authentication endpoints
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private')
  res.setHeader('Pragma', 'no-cache')
  res.setHeader('Expires', '0')

  // Add security headers specific to authentication
  res.setHeader('X-Auth-Security', 'enabled')

  next()
}

/**
 * Rate limiting security headers
 * Adds rate limiting information to responses
 */
export const rateLimitHeaders = (req, res, next) => {
  // Add rate limit information to headers
  const originalSend = res.send.bind(res)

  res.send = function(data) {
    // Add rate limit headers if they exist
    if (req.rateLimit) {
      if (req.rateLimit.limit !== undefined) {
        res.setHeader('X-RateLimit-Limit', req.rateLimit.limit)
      }
      if (req.rateLimit.remaining !== undefined) {
        res.setHeader('X-RateLimit-Remaining', req.rateLimit.remaining)
      }
      if (req.rateLimit.reset !== undefined) {
        res.setHeader('X-RateLimit-Reset', req.rateLimit.reset)
      }
    }

    return originalSend(data)
  }

  next()
}

/**
 * Security audit logging
 * Logs security-related events for monitoring
 */
export const securityAuditLog = (req, res, next) => {
  // Log security events in production
  if (process.env.NODE_ENV === 'production') {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      method: req.method,
      path: req.path,
      secure: req.secure,
      headers: {
        'x-forwarded-proto': req.headers['x-forwarded-proto'],
        'x-forwarded-ssl': req.headers['x-forwarded-ssl']
      }
    }

    // In production, this should be sent to a proper logging service
    console.log('Security Event:', JSON.stringify(securityEvent))
  }

  next()
}

/**
 * IP whitelist middleware (for admin endpoints)
 * Restricts access to specific IP addresses
 */
export const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    if (allowedIPs.length === 0) {
      return next() // No restrictions if no IPs specified
    }

    const clientIP = req.ip || req.connection.remoteAddress

    if (!allowedIPs.includes(clientIP)) {
      return next(errors.FORBIDDEN('Access denied from this IP address'))
    }

    next()
  }
}

/**
 * Security configuration object
 * Centralized security settings
 */
export const securityConfig = {
  // HTTPS settings
  https: {
    enforceInProduction: true,
    hstsMaxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },

  // Cookie settings
  cookies: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  },

  // CSRF settings
  csrf: {
    tokenLength: 32,
    tokenExpiry: 60 * 60 * 1000, // 1 hour
    cookieName: 'csrf-token'
  },

  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    authMaxRequests: 5
  }
}

/**
 * Combined security middleware
 * Applies all security measures in the correct order
 */
export const applySecurity = [
  enforceHTTPS,
  securityHeaders,
  secureCookies,
  rateLimitHeaders,
  securityAuditLog
]

/**
 * Authentication-specific security middleware stack
 */
export const applyAuthSecurity = [
  ...applySecurity,
  authSecurity
]
