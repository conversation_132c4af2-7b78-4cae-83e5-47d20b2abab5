/**
 * Password Reset Security Middleware
 * Enhanced security measures for password reset operations
 */

import rateLimit from 'express-rate-limit'
import { errors } from './errorHandler.js'

/**
 * Password reset token storage
 * In production, this should use Redis or database
 */
const resetTokens = new Map()
const resetAttempts = new Map()

/**
 * Password reset token data structure
 */
class ResetToken {
  constructor(email, token, expiresIn = 30 * 60 * 1000) { // 30 minutes default
    this.email = email
    this.token = token
    this.createdAt = new Date()
    this.expiresAt = new Date(Date.now() + expiresIn)
    this.used = false
    this.attempts = 0
  }

  isExpired() {
    return Date.now() > this.expiresAt.getTime()
  }

  isUsed() {
    return this.used
  }

  markAsUsed() {
    this.used = true
  }

  incrementAttempts() {
    this.attempts++
  }
}

/**
 * Enhanced rate limiting for password reset requests
 * More restrictive than general authentication rate limiting
 * Disabled in test environment
 */
export const passwordResetRateLimit = process.env.NODE_ENV === 'test'
  ? (req, res, next) => next() // No-op middleware in test
  : rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 3, // Only 3 reset requests per 15 minutes per IP
      message: {
        success: false,
        error: {
          code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED',
          message: 'Too many password reset requests. Please try again later.',
          timestamp: new Date().toISOString()
        }
      },
      standardHeaders: true,
      legacyHeaders: false
    })

/**
 * Email-based rate limiting for password reset
 * Prevents abuse of specific email addresses
 * Disabled in test environment
 */
export const emailResetRateLimit = (req, res, next) => {
  // Skip rate limiting in test environment
  if (process.env.NODE_ENV === 'test') {
    return next()
  }

  const email = req.body?.email

  if (!email) {
    return next()
  }

  const now = Date.now()
  const emailKey = `email:${email}`
  const attempts = resetAttempts.get(emailKey) || []

  // Clean up attempts older than 1 hour
  const recentAttempts = attempts.filter(timestamp => now - timestamp < 60 * 60 * 1000)

  // Allow maximum 5 reset requests per email per hour
  if (recentAttempts.length >= 5) {
    return res.status(429).json({
      success: false,
      error: {
        code: 'TOO_MANY_REQUESTS',
        message: 'Too many password reset requests for this email address',
        timestamp: new Date().toISOString()
      }
    })
  }

  // Record this attempt
  recentAttempts.push(now)
  resetAttempts.set(emailKey, recentAttempts)

  next()
}

/**
 * Store password reset token with enhanced security
 * @param {string} email - User email
 * @param {string} token - Reset token
 * @param {number} expiresIn - Expiration time in milliseconds
 */
export const storeResetToken = (email, token, expiresIn = 30 * 60 * 1000) => {
  const resetToken = new ResetToken(email, token, expiresIn)
  resetTokens.set(token, resetToken)

  // Also store by email for lookup
  resetTokens.set(`email:${email}`, token)

  return resetToken
}

/**
 * Validate password reset token
 * @param {string} token - Reset token to validate
 * @returns {Object} Validation result
 */
export const validateResetToken = (token) => {
  const resetToken = resetTokens.get(token)

  if (!resetToken) {
    return {
      valid: false,
      reason: 'Token not found or invalid'
    }
  }

  if (resetToken.isExpired()) {
    // Clean up expired token
    resetTokens.delete(token)
    resetTokens.delete(`email:${resetToken.email}`)

    return {
      valid: false,
      reason: 'Token has expired'
    }
  }

  if (resetToken.isUsed()) {
    return {
      valid: false,
      reason: 'Token has already been used'
    }
  }

  // Increment attempt counter
  resetToken.incrementAttempts()

  // Allow maximum 3 attempts per token
  if (resetToken.attempts > 3) {
    resetToken.markAsUsed()
    return {
      valid: false,
      reason: 'Too many attempts with this token'
    }
  }

  return {
    valid: true,
    email: resetToken.email,
    token: resetToken
  }
}

/**
 * Mark reset token as used (one-time usage enforcement)
 * @param {string} token - Reset token to mark as used
 */
export const markTokenAsUsed = (token) => {
  const resetToken = resetTokens.get(token)

  if (resetToken) {
    resetToken.markAsUsed()

    // Don't remove from storage immediately to allow proper validation
    // The cleanup process will handle removal later
  }
}

/**
 * Invalidate all reset tokens for an email
 * @param {string} email - Email address
 */
export const invalidateEmailResetTokens = (email) => {
  const existingToken = resetTokens.get(`email:${email}`)

  if (existingToken) {
    resetTokens.delete(existingToken)
    resetTokens.delete(`email:${email}`)
  }
}

/**
 * Password reset token validation middleware
 */
export const validateResetTokenMiddleware = (req, res, next) => {
  const { token } = req.body

  if (!token) {
    return next(errors.VALIDATION_ERROR('Reset token is required'))
  }

  const validation = validateResetToken(token)

  if (!validation.valid) {
    return next(errors.UNAUTHORIZED(validation.reason))
  }

  // Attach validated token data to request
  req.resetToken = validation.token
  req.resetEmail = validation.email

  next()
}

/**
 * Progressive delay for repeated reset attempts
 * Implements exponential backoff for security
 * Disabled in test environment
 */
export const progressiveDelayMiddleware = (req, res, next) => {
  // Skip delay in test environment
  if (process.env.NODE_ENV === 'test') {
    return next()
  }

  const email = req.body?.email

  if (!email) {
    return next()
  }

  const attempts = resetAttempts.get(`email:${email}`) || []
  const recentAttempts = attempts.filter(timestamp =>
    Date.now() - timestamp < 60 * 60 * 1000 // Last hour
  )

  // Calculate delay based on number of attempts
  let delayMs = 0
  if (recentAttempts.length >= 2) {
    delayMs = Math.min(1000 * Math.pow(2, recentAttempts.length - 2), 30000) // Max 30 seconds
  }

  if (delayMs > 0) {
    setTimeout(() => next(), delayMs)
  } else {
    next()
  }
}

/**
 * Clean up expired tokens and attempts
 * Should be called periodically
 */
export const cleanupExpiredData = () => {
  const now = Date.now()
  let cleanedTokens = 0
  let cleanedAttempts = 0

  // Clean up expired tokens
  for (const [key, value] of resetTokens.entries()) {
    if (value instanceof ResetToken && value.isExpired()) {
      resetTokens.delete(key)
      resetTokens.delete(`email:${value.email}`)
      cleanedTokens++
    }
  }

  // Clean up old attempts
  for (const [key, attempts] of resetAttempts.entries()) {
    const recentAttempts = attempts.filter(timestamp => now - timestamp < 60 * 60 * 1000)
    if (recentAttempts.length === 0) {
      resetAttempts.delete(key)
      cleanedAttempts++
    } else if (recentAttempts.length < attempts.length) {
      resetAttempts.set(key, recentAttempts)
    }
  }

  if (cleanedTokens > 0 || cleanedAttempts > 0) {
    console.log(`Cleaned up ${cleanedTokens} expired tokens and ${cleanedAttempts} old attempts`)
  }
}

/**
 * Get reset token statistics
 * @returns {Object} Token statistics
 */
export const getResetTokenStats = () => {
  let activeTokens = 0
  let expiredTokens = 0
  let usedTokens = 0

  for (const value of resetTokens.values()) {
    if (value instanceof ResetToken) {
      if (value.isExpired()) {
        expiredTokens++
      } else if (value.isUsed()) {
        usedTokens++
      } else {
        activeTokens++
      }
    }
  }

  return {
    activeTokens,
    expiredTokens,
    usedTokens,
    totalAttempts: resetAttempts.size
  }
}

/**
 * Security configuration for password reset
 */
export const passwordResetConfig = {
  tokenExpiryMs: 30 * 60 * 1000, // 30 minutes
  maxAttemptsPerToken: 3,
  maxRequestsPerIP: 3, // per 15 minutes
  maxRequestsPerEmail: 5, // per hour
  progressiveDelayEnabled: true,
  cleanupIntervalMs: 15 * 60 * 1000 // 15 minutes
}

// Cleanup expired data every 15 minutes
setInterval(cleanupExpiredData, passwordResetConfig.cleanupIntervalMs)
