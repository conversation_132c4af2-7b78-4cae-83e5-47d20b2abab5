/**
 * CSRF Protection Middleware
 * Implements Cross-Site Request Forgery protection for authentication endpoints
 */

import crypto from 'crypto'
import { errors } from './errorHandler.js'
import { traceEntry, traceError, traceExit } from '../utils/traceLogger.js'

/**
 * CSRF token storage (in production, use Redis or database)
 * Maps session ID to CSRF token
 */
const csrfTokens = new Map()

/**
 * Generate a secure CSRF token
 * @returns {string} CSRF token
 */
function createCSRFToken() {
  traceEntry('createCSRFToken')
  try {
    const token = crypto.randomBytes(32).toString('hex')
    return token
  } catch (error) {
    traceError('createCSRFToken', error)
    throw error
  } finally {
    traceExit('createCSRFToken')
  }
}

/**
 * Get session ID from request (using IP + User-Agent as session identifier)
 * In production, this should use actual session management
 * @param {Request} req - Express request object
 * @returns {string} Session identifier
 */
function getSessionId(req) {
  traceEntry('getSessionId')
  try {
    // Use IP + User-Agent as session identifier for CSRF tokens
    // This allows tokens to persist across requests from the same client
    const ip = req.ip || req.connection?.remoteAddress || 'unknown'
    const userAgent = req.headers['user-agent'] || 'unknown'
    return `${ip}:${userAgent}`
  } catch (error) {
    traceError('getSessionId', error)
    throw error
  } finally {
    traceExit('getSessionId')
  }
}

/**
 * CSRF token generation middleware
 * Generates and stores CSRF tokens for authenticated sessions
 */
export const generateCSRFToken = (req, res, next) => {
  traceEntry('generateCSRFToken')
  try {
    const sessionId = getSessionId(req)
    const token = createCSRFToken()

    // Store token with expiration (1 hour)
    csrfTokens.set(sessionId, {
      token,
      expires: Date.now() + (60 * 60 * 1000) // 1 hour
    })

    // Set CSRF token in secure cookie
    res.cookie('csrf-token', token, {
      httpOnly: false, // Allow JavaScript access for AJAX requests
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      sameSite: 'strict',
      maxAge: 60 * 60 * 1000 // 1 hour
    })

    // Also provide token in response header for SPA usage
    res.setHeader('X-CSRF-Token', token)

    next()
  } catch (error) {
    traceError('generateCSRFToken', error)
    next(errors.INTERNAL_SERVER_ERROR('Failed to generate CSRF token'))
  } finally {
    traceExit('generateCSRFToken')
  }
}

/**
 * CSRF token validation middleware
 * Validates CSRF tokens for state-changing operations
 */
export const validateCSRFToken = (req, res, next) => {
  traceEntry('validateCSRFToken')
  try {
    // Skip CSRF validation for GET, HEAD, OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next()
    }

    const sessionId = getSessionId(req)
    const tokenFromHeader = req.headers['x-csrf-token']
    const tokenFromBody = req.body?.csrfToken
    const tokenFromCookie = req.cookies?.['csrf-token']

    // Get token from header, body, or cookie
    const providedToken = tokenFromHeader || tokenFromBody || tokenFromCookie

    if (!providedToken) {
      return next(errors.FORBIDDEN('CSRF token missing'))
    }

    // Get stored token
    const storedTokenData = csrfTokens.get(sessionId)

    if (!storedTokenData) {
      return next(errors.FORBIDDEN('CSRF token not found for session'))
    }

    // Check if token has expired
    if (Date.now() > storedTokenData.expires) {
      csrfTokens.delete(sessionId)
      return next(errors.FORBIDDEN('CSRF token expired'))
    }

    // Validate token
    if (providedToken !== storedTokenData.token) {
      return next(errors.FORBIDDEN('Invalid CSRF token'))
    }

    next()
  } catch (error) {
    traceError('validateCSRFToken', error)
    next(errors.INTERNAL_SERVER_ERROR('CSRF validation failed'))
  } finally {
    traceExit('validateCSRFToken')
  }
}

/**
 * CSRF protection for authentication routes
 * Applies CSRF validation to specific authentication endpoints
 */
export const csrfProtection = (req, res, next) => {
  traceEntry('csrfProtection')
  try {
    // Apply CSRF protection to state-changing authentication operations
    const protectedPaths = [
      '/login',
      '/register',
      '/logout',
      '/forgot-password',
      '/reset-password',
      '/profile',
      '/password'
    ]

    const isProtectedPath = protectedPaths.some(path =>
      req.path.endsWith(path) || req.path.includes(path)
    )

    if (isProtectedPath && !['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return validateCSRFToken(req, res, next)
    }

    next()
  } catch (error) {
    traceError('csrfProtection', error)
    next(error)
  } finally {
    traceExit('csrfProtection')
  }
}

/**
 * Cleanup expired CSRF tokens
 * Should be called periodically to prevent memory leaks
 */
export const cleanupExpiredTokens = () => {
  traceEntry('cleanupExpiredTokens')
  try {
    const now = Date.now()
    for (const [sessionId, tokenData] of csrfTokens.entries()) {
      if (now > tokenData.expires) {
        csrfTokens.delete(sessionId)
      }
    }
  } catch (error) {
    traceError('cleanupExpiredTokens', error)
    throw error
  } finally {
    traceExit('cleanupExpiredTokens')
  }
}

/**
 * Get CSRF token for current session
 * Utility function for retrieving current CSRF token
 */
export const getCSRFToken = (req) => {
  traceEntry('getCSRFToken')
  try {
    const sessionId = getSessionId(req)
    const tokenData = csrfTokens.get(sessionId)
    return tokenData && Date.now() <= tokenData.expires ? tokenData.token : null
  } catch (error) {
    traceError('getCSRFToken', error)
    throw error
  } finally {
    traceExit('getCSRFToken')
  }
}

/**
 * CSRF token endpoint
 * Provides CSRF token for AJAX requests
 */
export const csrfTokenEndpoint = (req, res) => {
  traceEntry('csrfTokenEndpoint')
  try {
    // Check if token already exists for this session
    let token = getCSRFToken(req)

    if (!token) {
      // Generate new token if none exists
      generateCSRFToken(req, res, () => {
        const newToken = getCSRFToken(req)
        res.json({
          success: true,
          data: {
            csrfToken: newToken
          }
        })
      })
    } else {
      // Return existing token
      res.json({
        success: true,
        data: {
          csrfToken: token
        }
      })
    }
  } catch (error) {
    traceError('csrfTokenEndpoint', error)
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to retrieve CSRF token',
        timestamp: new Date().toISOString()
      }
    })
  } finally {
    traceExit('csrfTokenEndpoint')
  }
}

// Cleanup expired tokens every 15 minutes
setInterval(cleanupExpiredTokens, 15 * 60 * 1000)
