/**
 * Trace Logger Utility
 * Provides comprehensive trace logging with configurable feature flag support
 */

/**
 * Trace logging configuration
 */
let _traceConfig = null
const TRACE_CONFIG = new Proxy({}, {
  get: (target, prop) => {
    if (!_traceConfig) {
      _traceConfig = {
        enabled: process.env.ENABLE_TRACE_LOGGING === 'true',
        level: process.env.TRACE_LOG_LEVEL || 'debug',
        format: process.env.TRACE_LOG_FORMAT || 'json'
      }
    }
    return _traceConfig[prop]
  }
})

/**
 * Log levels for filtering
 */
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
  trace: 4
}

let _currentLevel = 1

function incrementHierarchyLevel() {
  _currentLevel++
  hierarchy = generateHierarchyNotation(_currentLevel)
}

function decrementHierarchyLevel() {
  _currentLevel--
  hierarchy = generateHierarchyNotation(_currentLevel)
}

function generateHierarchyNotation(level) {
  return '-'.repeat(level) + '> '
}

let hierarchy = generateHierarchyNotation(_currentLevel)

/**
 * Get current log level number
 */
function getCurrentLogLevel() {
  return LOG_LEVELS[TRACE_CONFIG.level] || LOG_LEVELS.debug
}

/**
 * Check if trace logging is enabled and level is appropriate
 * @param {string} level - Log level to check
 * @returns {boolean} Whether logging should occur
 */
function shouldLog(level = 'debug') {
  if (!TRACE_CONFIG.enabled) return false
  return LOG_LEVELS[level] <= getCurrentLogLevel()
}

/**
 * Format trace log entry
 * @param {string} type - Log type (ENTRY, EXIT, HTTP_REQUEST, etc.)
 * @param {string} name - Function/endpoint name
 * @param {Object} data - Additional data to log
 * @param {string} level - Log level
 * @returns {Object} Formatted log entry
 */
function formatTraceLog(type, name, data = {}, level = 'debug') {

  const timestamp = new Date().toISOString()

  if (TRACE_CONFIG.format === 'json') {
    const logEntry = {
      timestamp,
      type: `TRACE_${type}`,
      level: level.toUpperCase(),
      hierarchy,
      name,
      ...data
    }

    return JSON.stringify(logEntry)

  } else {

    // Simple text format
    const dataStr = Object.keys(data).length > 0 ? ` | ${JSON.stringify(data)}` : ''
    return `[${timestamp}] TRACE_${type} ${hierarchy} ${name}${dataStr}`

  }
}

/**
 * Core trace logging function
 * @param {string} type - Log type
 * @param {string} name - Function/endpoint name
 * @param {Object} data - Additional data
 * @param {string} level - Log level
 */
function trace(type, name, data = {}, level = 'debug') {
  if (!shouldLog(level)) return

  const logMessage = formatTraceLog(type, name, data, level)

  // Use appropriate console method based on level
  switch (level) {
    case 'error':
      console.error(logMessage)
      break
    case 'warn':
      console.warn(logMessage)
      break
    case 'info':
      console.info(logMessage)
      break
    default:
      console.log(logMessage)
  }
}

/**
 * Trace function entry
 * @param {string} functionName - Name of the function being entered
 * @param {Object} args - Function arguments
 * @param {Object} context - Additional context (req.method, req.url, etc.)
 */
function traceEntry(functionName, args = {}, context = {}) {
  incrementHierarchyLevel()
  trace('ENTRY', functionName, {
    args,
    context
  })
}

/**
 * Trace function exit
 * @param {string} functionName - Name of the function being exited
 * @param {Object} result - Function result/response
 * @param {number} duration - Execution duration in ms
 */
function traceExit(functionName, result = {}, duration = null) {
  const data = { result }
  if (duration !== null) {
    data.duration_ms = duration
  }
  decrementHierarchyLevel()

  trace('EXIT', functionName, data)
}

/**
 * Trace HTTP request
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {Object} headers - Request headers
 * @param {Object} body - Request body
 * @param {string} requestId - Request ID for correlation
 */
function traceHttpRequest(method, url, headers = {}, body = {}, requestId = null) {
  trace('HTTP_REQUEST', `${method} ${url}`, {
    method,
    url,
    headers: sanitizeHeaders(headers),
    body: sanitizeBody(body),
    request_id: requestId
  })
}

/**
 * Trace HTTP response
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {number} statusCode - Response status code
 * @param {Object} headers - Response headers
 * @param {Object} body - Response body
 * @param {number} duration - Request duration in ms
 * @param {string} requestId - Request ID for correlation
 */
function traceHttpResponse(method, url, statusCode, headers = {}, body = {}, duration = null, requestId = null) {
  const data = {
    method,
    url,
    status_code: statusCode,
    headers: sanitizeHeaders(headers),
    body: sanitizeBody(body),
    request_id: requestId
  }

  if (duration !== null) {
    data.duration_ms = duration
  }

  trace('HTTP_RESPONSE', `${method} ${url}`, data)
}

/**
 * Trace error
 * @param {string} context - Error context
 * @param {Error} error - Error object
 * @param {Object} additionalData - Additional error context
 */
function traceError(context, error, additionalData = {}) {
  trace('ERROR', context, {
    error_message: error.message,
    error_name: error.name,
    error_stack: error.stack,
    ...additionalData
  }, 'error')
}

/**
 * Sanitize headers for logging (remove sensitive data)
 * @param {Object} headers - Headers object
 * @returns {Object} Sanitized headers
 */
function sanitizeHeaders(headers) {
  const sanitized = { ...headers }
  const sensitiveHeaders = ['authorization', 'cookie', 'x-csrf-token', 'x-api-key']

  sensitiveHeaders.forEach(header => {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]'
    }
    if (sanitized[header.toLowerCase()]) {
      sanitized[header.toLowerCase()] = '[REDACTED]'
    }
  })

  return sanitized
}

/**
 * Sanitize body for logging (remove sensitive data)
 * @param {Object} body - Body object
 * @returns {Object} Sanitized body
 */
function sanitizeBody(body) {
  if (!body || typeof body !== 'object') return body

  const sanitized = { ...body }
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth']

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]'
    }
  })

  return sanitized
}

/**
 * Create a trace wrapper for async functions
 * @param {string} functionName - Function name
 * @param {Function} fn - Function to wrap
 * @returns {Function} Wrapped function
 */
function traceWrapper(functionName, fn) {
  return async function(...args) {
    const startTime = Date.now()

    traceEntry(functionName, { args })

    try {
      const result = await fn.apply(this, args)
      const duration = Date.now() - startTime
      traceExit(functionName, { result }, duration)
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      traceError(functionName, error, { duration_ms: duration })
      throw error
    }
  }
}

/**
 * Get trace configuration (for debugging)
 * @returns {Object} Current trace configuration
 */
function getTraceConfig() {
  return { ...TRACE_CONFIG }
}

export {
  trace,
  traceEntry,
  traceExit,
  traceHttpRequest,
  traceHttpResponse,
  traceError,
  traceWrapper,
  getTraceConfig,
  shouldLog
}
