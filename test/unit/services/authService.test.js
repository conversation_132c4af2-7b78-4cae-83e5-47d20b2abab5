/**
 * Authentication Service Unit Tests
 * Tests for authentication business logic
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { AuthService } from '../../../api/services/authService.js'
import { createDatabaseService } from '../../../common/services/databaseService.js'
import { validateResetToken, markTokenAsUsed } from '../../../api/middleware/passwordReset.js'

// Mock the password reset middleware
vi.mock('../../../api/middleware/passwordReset.js', () => ({
  validateResetToken: vi.fn(),
  markTokenAsUsed: vi.fn(),
  invalidateEmailResetTokens: vi.fn(),
  storeResetToken: vi.fn()
}))

// Mock the database service
vi.mock('../../../common/services/databaseService.js', () => {
  // Create mock objects inside the factory function
  const mockUsersCollection = {
    authWithPassword: vi.fn(),
    create: vi.fn(),
    requestVerification: vi.fn(),
    requestPasswordReset: vi.fn(),
    confirmPasswordReset: vi.fn(),
    getOne: vi.fn(),
    update: vi.fn()
  }

  const mockPocketBase = {
    collection: vi.fn(() => mockUsersCollection),
    authStore: {
      isValid: true,
      token: 'mock-token'
    }
  }

  return {
    createDatabaseService: vi.fn(() => Promise.resolve({
      db: mockPocketBase
    })),
    getDatabaseService: vi.fn(() => Promise.resolve({
      db: mockPocketBase
    }))
  }
})

vi.mock('../../../common/validation/index.js', () => ({
  sanitizeData: vi.fn((data) => data)
}))

vi.mock('../../../api/middleware/errorHandler.js', () => ({
  errors: {
    UNAUTHORIZED: vi.fn((message) => ({ code: 'UNAUTHORIZED', message })),
    VALIDATION_ERROR: vi.fn((message) => ({ code: 'VALIDATION_ERROR', message })),
    NOT_FOUND: vi.fn((message) => ({ code: 'NOT_FOUND', message })),
    INTERNAL_SERVER_ERROR: vi.fn((message) => ({ code: 'INTERNAL_SERVER_ERROR', message }))
  }
}))

describe('AuthService', () => {
  let mockPocketBase
  let mockUsersCollection

  beforeEach(async () => {
    // Get the mocked database service
    const mockedCreateDatabaseService = vi.mocked(createDatabaseService)
    const dbService = await mockedCreateDatabaseService()
    mockPocketBase = dbService.db
    mockUsersCollection = mockPocketBase.collection()

    // Setup password reset middleware mocks
    vi.mocked(validateResetToken).mockReturnValue({
      valid: true,
      email: '<EMAIL>',
      token: { email: '<EMAIL>' }
    })
    vi.mocked(markTokenAsUsed).mockReturnValue(undefined)

    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('login', () => {
    it('should login user with valid credentials', async () => {
      const mockAuthData = {
        record: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          avatar: null,
          role: 'user',
          verified: true,
          created: '2023-01-01T00:00:00Z',
          updated: '2023-01-01T00:00:00Z'
        },
        token: 'jwt-token'
      }

      mockUsersCollection.authWithPassword.mockResolvedValue(mockAuthData)

      const result = await AuthService.login('<EMAIL>', 'password123', true)

      expect(result.success).toBe(true)
      expect(result.user.email).toBe('<EMAIL>')
      expect(result.token).toBe('jwt-token')
      expect(mockUsersCollection.authWithPassword).toHaveBeenCalledWith('<EMAIL>', 'password123')
    })

    it('should throw error for invalid credentials', async () => {
      mockUsersCollection.authWithPassword.mockRejectedValue({ status: 400 })

      await expect(AuthService.login('<EMAIL>', 'wrongpassword'))
        .rejects.toMatchObject({ code: 'UNAUTHORIZED' })
    })

    it('should throw error when no record returned', async () => {
      mockUsersCollection.authWithPassword.mockResolvedValue({ record: null })

      await expect(AuthService.login('<EMAIL>', 'password123'))
        .rejects.toMatchObject({ code: 'UNAUTHORIZED' })
    })
  })

  describe('register', () => {
    it('should register user with valid data', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        verified: false
      }

      mockUsersCollection.create.mockResolvedValue(mockUser)
      mockUsersCollection.requestVerification.mockResolvedValue()

      const result = await AuthService.register({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        passwordConfirm: 'Password123'
      })

      expect(result.success).toBe(true)
      expect(result.user.email).toBe('<EMAIL>')
      expect(mockUsersCollection.create).toHaveBeenCalledWith({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        passwordConfirm: 'Password123',
        role: 'user'
      })
      expect(mockUsersCollection.requestVerification).toHaveBeenCalledWith('<EMAIL>')
    })

    it('should handle registration validation errors', async () => {
      mockUsersCollection.create.mockRejectedValue({
        status: 400,
        data: { data: { email: 'Email already exists' } }
      })

      await expect(AuthService.register({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        passwordConfirm: 'Password123'
      })).rejects.toMatchObject({ code: 'VALIDATION_ERROR' })
    })

    it('should continue registration even if verification email fails', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        verified: false
      }

      mockUsersCollection.create.mockResolvedValue(mockUser)
      mockUsersCollection.requestVerification.mockRejectedValue(new Error('Email service unavailable'))

      const result = await AuthService.register({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        passwordConfirm: 'Password123'
      })

      expect(result.success).toBe(true)
      expect(result.user.email).toBe('<EMAIL>')
    })
  })

  describe('logout', () => {
    it('should logout successfully', async () => {
      const result = await AuthService.logout('jwt-token')

      expect(result.success).toBe(true)
      expect(result.message).toBe('Logout successful')
    })
  })

  describe('refreshToken', () => {
    it('should refresh token when valid', async () => {
      mockPocketBase.authStore.isValid = true
      mockPocketBase.authStore.token = 'new-token'

      const result = await AuthService.refreshToken('refresh-token')

      expect(result.success).toBe(true)
      expect(result.token).toBe('new-token')
    })

    it('should throw error for invalid token', async () => {
      mockPocketBase.authStore.isValid = false

      await expect(AuthService.refreshToken('invalid-token'))
        .rejects.toMatchObject({ code: 'UNAUTHORIZED' })
    })
  })

  describe('requestPasswordReset', () => {
    it('should request password reset', async () => {
      mockUsersCollection.requestPasswordReset.mockResolvedValue()

      const result = await AuthService.requestPasswordReset('<EMAIL>')

      expect(result.success).toBe(true)
      expect(result.message).toContain('Password reset link sent')
      expect(mockUsersCollection.requestPasswordReset).toHaveBeenCalledWith('<EMAIL>')
    })

    it('should return success even if email does not exist', async () => {
      mockUsersCollection.requestPasswordReset.mockRejectedValue(new Error('User not found'))

      const result = await AuthService.requestPasswordReset('<EMAIL>')

      expect(result.success).toBe(true)
      expect(result.message).toContain('Password reset link sent')
    })
  })

  describe('resetPassword', () => {
    it('should reset password with valid token', async () => {
      mockUsersCollection.confirmPasswordReset.mockResolvedValue()

      const result = await AuthService.resetPassword('reset-token', 'NewPassword123', 'NewPassword123')

      expect(result.success).toBe(true)
      expect(result.message).toBe('Password reset successful')
      expect(mockUsersCollection.confirmPasswordReset).toHaveBeenCalledWith('reset-token', 'NewPassword123', 'NewPassword123')
      expect(vi.mocked(validateResetToken)).toHaveBeenCalledWith('reset-token')
      expect(vi.mocked(markTokenAsUsed)).toHaveBeenCalledWith('reset-token')
    })

    it('should throw error for invalid token', async () => {
      vi.mocked(validateResetToken).mockReturnValue({
        valid: false,
        reason: 'Token not found or invalid'
      })

      await expect(AuthService.resetPassword('invalid-token', 'NewPassword123', 'NewPassword123'))
        .rejects.toMatchObject({ code: 'UNAUTHORIZED' })
    })
  })

  describe('getCurrentUser', () => {
    it('should get current user profile', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar: null,
        role: 'user',
        verified: true,
        created: '2023-01-01T00:00:00Z',
        updated: '2023-01-01T00:00:00Z'
      }

      mockUsersCollection.getOne.mockResolvedValue(mockUser)

      const result = await AuthService.getCurrentUser('user-123')

      expect(result.success).toBe(true)
      expect(result.user.email).toBe('<EMAIL>')
      expect(mockUsersCollection.getOne).toHaveBeenCalledWith('user-123')
    })

    it('should throw error for non-existent user', async () => {
      mockUsersCollection.getOne.mockRejectedValue({ status: 404 })

      await expect(AuthService.getCurrentUser('non-existent'))
        .rejects.toMatchObject({ code: 'NOT_FOUND' })
    })
  })

  describe('updateProfile', () => {
    it('should update user profile', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Updated Name',
        avatar: null,
        role: 'user',
        verified: true
      }

      mockUsersCollection.update.mockResolvedValue(mockUser)

      const result = await AuthService.updateProfile('user-123', {
        name: 'Updated Name',
        email: '<EMAIL>'
      })

      expect(result.success).toBe(true)
      expect(result.user.name).toBe('Updated Name')
      expect(mockUsersCollection.update).toHaveBeenCalledWith('user-123', {
        name: 'Updated Name',
        email: '<EMAIL>'
      })
    })

    it('should handle validation errors', async () => {
      mockUsersCollection.update.mockRejectedValue({
        status: 400,
        data: { data: { email: 'Email already exists' } }
      })

      await expect(AuthService.updateProfile('user-123', {
        email: '<EMAIL>'
      })).rejects.toMatchObject({ code: 'VALIDATION_ERROR' })
    })
  })

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      mockUsersCollection.update.mockResolvedValue()

      const result = await AuthService.changePassword('user-123', 'oldPassword', 'NewPassword123', 'NewPassword123')

      expect(result.success).toBe(true)
      expect(result.message).toBe('Password changed successfully')
      expect(mockUsersCollection.update).toHaveBeenCalledWith('user-123', {
        oldPassword: 'oldPassword',
        password: 'NewPassword123',
        passwordConfirm: 'NewPassword123'
      })
    })

    it('should throw error for incorrect current password', async () => {
      mockUsersCollection.update.mockRejectedValue({
        status: 400,
        message: 'oldPassword is invalid'
      })

      await expect(AuthService.changePassword('user-123', 'wrongPassword', 'NewPassword123', 'NewPassword123'))
        .rejects.toMatchObject({ code: 'UNAUTHORIZED' })
    })
  })
})
