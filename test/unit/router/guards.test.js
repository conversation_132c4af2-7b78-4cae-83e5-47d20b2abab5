/**
 * Route Guard Unit Tests (TEST-003)
 * Tests for route guard functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'

// Mock the auth store
const mockAuthStore = {
  isAuthenticated: false,
  user: null,
  initialized: false,
  initializeAuth: vi.fn(),
}

vi.mock('../../../ui/stores/auth.js', () => ({
  useAuthStore: () => mockAuthStore,
}))

// Mock route objects
const createMockRoute = (path, meta = {}) => ({
  path,
  name: path.replace('/', '') || 'home',
  meta,
  fullPath: path,
  query: {},
  params: {},
})

const createMockNext = () => {
  const next = vi.fn()
  next.mockImplementation((to) => {
    if (to) {
      next.calledWith = to
    }
  })
  return next
}

// Route guard implementation for testing
const authGuard = async (to, from, next) => {
  const authStore = mockAuthStore

  // Initialize auth store if not already initialized
  if (!authStore.initialized) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      // Continue navigation even if initialization fails
      console.warn('Auth initialization failed:', error.message)
    }
  }

  const isAuthenticated = authStore.isAuthenticated
  const isProtectedRoute = to.meta.requiresAuth
  const isGuestOnlyRoute = to.meta.requiresGuest
  const requiresAdmin = to.meta.requiresAdmin

  // Redirect authenticated users away from guest-only routes
  if (isAuthenticated && isGuestOnlyRoute) {
    next({ path: '/' })
    return
  }

  // Redirect unauthenticated users away from protected routes
  if (!isAuthenticated && isProtectedRoute) {
    next({
      path: '/login',
      query: { redirect: to.fullPath },
    })
    return
  }

  // Check admin requirements (only for authenticated users)
  if (requiresAdmin) {
    if (!isAuthenticated) {
      next({ path: '/unauthorized' })
      return
    }

    if (authStore.user?.role !== 'admin') {
      next({ path: '/unauthorized' })
      return
    }
  }

  // Allow navigation
  next()
}

describe('Route Guards (TEST-003)', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    mockAuthStore.isAuthenticated = false
    mockAuthStore.user = null
    mockAuthStore.initialized = false
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Authentication Guard', () => {
    it('should initialize auth store if not initialized', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.initialized = false
      mockAuthStore.initializeAuth.mockResolvedValue()

      await authGuard(to, from, next)

      expect(mockAuthStore.initializeAuth).toHaveBeenCalled()
      expect(next).toHaveBeenCalledWith()
    })

    it('should not initialize auth store if already initialized', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(mockAuthStore.initializeAuth).not.toHaveBeenCalled()
      expect(next).toHaveBeenCalledWith()
    })
  })

  describe('Protected Routes', () => {
    it('should allow access to protected routes when authenticated', async () => {
      const to = createMockRoute('/profile', { requiresAuth: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith()
    })

    it('should redirect to login when accessing protected routes while unauthenticated', async () => {
      const to = createMockRoute('/profile', { requiresAuth: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({
        path: '/login',
        query: { redirect: '/profile' },
      })
    })

    it('should preserve query parameters in redirect', async () => {
      const to = createMockRoute('/profile?tab=settings', { requiresAuth: true })
      to.fullPath = '/profile?tab=settings'
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({
        path: '/login',
        query: { redirect: '/profile?tab=settings' },
      })
    })
  })

  describe('Guest-Only Routes', () => {
    it('should allow access to guest-only routes when unauthenticated', async () => {
      const to = createMockRoute('/login', { requiresGuest: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith()
    })

    it('should redirect to home when accessing guest-only routes while authenticated', async () => {
      const to = createMockRoute('/login', { requiresGuest: true })
      const from = createMockRoute('/profile')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({ path: '/' })
    })

    it('should redirect authenticated users from register page', async () => {
      const to = createMockRoute('/register', { requiresGuest: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({ path: '/' })
    })

    it('should redirect authenticated users from forgot-password page', async () => {
      const to = createMockRoute('/forgot-password', { requiresGuest: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({ path: '/' })
    })
  })

  describe('Admin Routes', () => {
    it('should allow access to admin routes when user is admin', async () => {
      const to = createMockRoute('/admin', { requiresAuth: true, requiresAdmin: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.user = { id: '1', email: '<EMAIL>', role: 'admin' }
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith()
    })

    it('should redirect to unauthorized when non-admin user accesses admin routes', async () => {
      const to = createMockRoute('/admin', { requiresAuth: true, requiresAdmin: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.user = { id: '1', email: '<EMAIL>', role: 'user' }
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({ path: '/unauthorized' })
    })

    it('should redirect to login when unauthenticated user accesses admin routes', async () => {
      const to = createMockRoute('/admin', { requiresAuth: true, requiresAdmin: true })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.user = null
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({
        path: '/login',
        query: { redirect: '/admin' }
      })
    })
  })

  describe('Public Routes', () => {
    it('should allow access to public routes regardless of authentication status', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith()
    })

    it('should allow authenticated users to access public routes', async () => {
      const to = createMockRoute('/help')
      const from = createMockRoute('/profile')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith()
    })
  })

  describe('Error Handling', () => {
    it('should handle auth initialization errors gracefully', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.initialized = false
      mockAuthStore.initializeAuth.mockRejectedValue(new Error('Auth initialization failed'))

      await authGuard(to, from, next)

      expect(mockAuthStore.initializeAuth).toHaveBeenCalled()
      // Should still allow navigation even if initialization fails
      expect(next).toHaveBeenCalledWith()
    })
  })

  describe('Route Meta Combinations', () => {
    it('should handle routes with multiple meta requirements', async () => {
      const to = createMockRoute('/admin/users', {
        requiresAuth: true,
        requiresAdmin: true,
      })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = true
      mockAuthStore.user = { id: '1', email: '<EMAIL>', role: 'admin' }
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith()
    })

    it('should prioritize authentication check over admin check', async () => {
      const to = createMockRoute('/admin/users', {
        requiresAuth: true,
        requiresAdmin: true,
      })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.user = null
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({
        path: '/login',
        query: { redirect: '/admin/users' },
      })
    })
  })
})
