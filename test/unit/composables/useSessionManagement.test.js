/**
 * Session Management Composable Unit Tests
 * Tests for session management functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useSessionManagement } from '../../../ui/composables/useSessionManagement.js'

// Mock dependencies
const mockAuthStore = {
  isAuthenticated: true,
  token: 'test-token',
  $subscribe: vi.fn()
}

vi.mock('../../../ui/stores/auth.js', () => ({
  useAuthStore: () => mockAuthStore
}))

// Mock fetch
global.fetch = vi.fn()

describe('useSessionManagement', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)

    // Reset auth store state
    mockAuthStore.isAuthenticated = true
    mockAuthStore.token = 'test-token'

    vi.clearAllMocks()
    vi.useFakeTimers()

    // Mock CSRF token response by default
    global.fetch.mockImplementation((url) => {
      if (url === '/api/auth/csrf-token') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
      }
      // Return a basic mock for other requests
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ success: true, data: {} })
      })
    })
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.resetAllMocks()
  })

  describe('initialization', () => {
    it('should initialize with empty sessions', () => {
      const {
        sessions,
        loading,
        error,
        totalSessions,
        hasMultipleSessions
      } = useSessionManagement()

      expect(sessions.value).toEqual([])
      expect(loading.value).toBe(false)
      expect(error.value).toBeNull()
      expect(totalSessions.value).toBe(0)
      expect(hasMultipleSessions.value).toBe(false)
    })
  })

  describe('fetchSessions', () => {
    it('should fetch sessions successfully', async () => {
      const mockSessions = [
        {
          id: 'session-1',
          userId: 'user-1',
          userAgent: 'Mozilla/5.0 Chrome',
          ip: '***********',
          isActive: true,
          isCurrent: true,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        },
        {
          id: 'session-2',
          userId: 'user-1',
          userAgent: 'Mozilla/5.0 Firefox',
          ip: '***********',
          isActive: true,
          isCurrent: false,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        }
      ]

      // Mock CSRF token call and sessions call
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { sessions: mockSessions }
          })
        })

      const { fetchSessions, sessions, loading, error } = useSessionManagement()

      const promise = fetchSessions()
      expect(loading.value).toBe(true)

      await promise

      expect(loading.value).toBe(false)
      expect(error.value).toBeNull()
      expect(sessions.value).toEqual(mockSessions)
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/sessions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
          'X-CSRF-Token': 'csrf-token-123'
        }
      })
    })

    it('should handle fetch errors', async () => {
      // Mock CSRF token success, then network error for sessions
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockRejectedValueOnce(new Error('Network error'))

      const { fetchSessions, sessions, loading, error } = useSessionManagement()

      await fetchSessions()

      expect(loading.value).toBe(false)
      expect(error.value).toBe('Network error')
      expect(sessions.value).toEqual([])
    })

    it('should handle API errors', async () => {
      // Mock CSRF token success, then API error for sessions
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          statusText: 'Unauthorized'
        })

      const { fetchSessions, error } = useSessionManagement()

      await fetchSessions()

      expect(error.value).toBe('HTTP 401: Unauthorized')
    })

    it('should handle API response errors', async () => {
      // Mock CSRF token success, then API response error for sessions
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: false,
            error: { message: 'Invalid session' }
          })
        })

      const { fetchSessions, error } = useSessionManagement()

      await fetchSessions()

      expect(error.value).toBe('Invalid session')
    })

    it('should not fetch when unauthenticated', async () => {
      // Set auth store to unauthenticated state
      mockAuthStore.isAuthenticated = false
      mockAuthStore.token = null

      const { fetchSessions } = useSessionManagement()

      await fetchSessions()

      expect(global.fetch).not.toHaveBeenCalled()
    })
  })

  describe('terminateSession', () => {
    it('should terminate session successfully', async () => {
      // Clear the default mock implementation
      global.fetch.mockClear()

      // We need to access the internal sessions ref directly for testing
      // Since the composable returns a computed, we'll use a different approach
      const composable = useSessionManagement()

      // Mock the internal sessions state by calling fetchSessions first
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1', isCurrent: true },
                { id: 'session-2', isCurrent: false }
              ]
            }
          })
        })

      await composable.fetchSessions()

      // Now mock the terminate session call
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: 'Session terminated'
          })
        })

      const result = await composable.terminateSession('session-2')

      expect(result.success).toBe(true)
      expect(composable.sessions.value).toHaveLength(1)
      expect(composable.sessions.value[0].id).toBe('session-1')
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/sessions/session-2', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
          'X-CSRF-Token': 'csrf-token-123'
        }
      })
    })

    it('should handle termination errors', async () => {
      // Mock CSRF token success, then network error for terminate
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockRejectedValueOnce(new Error('Network error'))

      const { terminateSession } = useSessionManagement()

      await expect(terminateSession('session-1')).rejects.toThrow('Network error')
    })

    it('should handle API termination errors', async () => {
      // Mock CSRF token success, then API error for terminate
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: 'Not Found'
        })

      const { terminateSession } = useSessionManagement()

      await expect(terminateSession('session-1')).rejects.toThrow('HTTP 404: Not Found')
    })
  })

  describe('terminateAllOtherSessions', () => {
    it('should terminate all other sessions successfully', async () => {
      const composable = useSessionManagement()

      // First set up initial sessions
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1', isCurrent: true },
                { id: 'session-2', isCurrent: false },
                { id: 'session-3', isCurrent: false }
              ]
            }
          })
        })

      await composable.fetchSessions()

      // Now mock the terminate all sessions call
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            message: '2 sessions terminated'
          })
        })

      const result = await composable.terminateAllOtherSessions()

      expect(result.success).toBe(true)
      expect(composable.sessions.value).toHaveLength(1)
      expect(composable.sessions.value[0].isCurrent).toBe(true)
    })

    it('should handle termination errors', async () => {
      // Mock CSRF token success, then network error for terminate all
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockRejectedValueOnce(new Error('Network error'))

      const { terminateAllOtherSessions } = useSessionManagement()

      await expect(terminateAllOtherSessions()).rejects.toThrow('Network error')
    })
  })

  describe('refreshCurrentSession', () => {
    it('should refresh current session successfully', async () => {
      // Mock extend session API
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true })
        })
        // Mock fetch sessions API
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { sessions: [] }
          })
        })

      const { refreshCurrentSession } = useSessionManagement()

      const result = await refreshCurrentSession()

      expect(result.success).toBe(true)
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/extend-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
          'X-CSRF-Token': expect.any(String)
        }
      })
    })

    it('should handle refresh errors', async () => {
      // Mock CSRF token success, then network error for refresh
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockRejectedValueOnce(new Error('Network error'))

      const { refreshCurrentSession } = useSessionManagement()

      await expect(refreshCurrentSession()).rejects.toThrow('Network error')
    })
  })

  describe('computed properties', () => {
    it('should compute active sessions correctly', async () => {
      const composable = useSessionManagement()

      // Mock sessions data
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1', isActive: true },
                { id: 'session-2', isActive: false },
                { id: 'session-3', isActive: true }
              ]
            }
          })
        })

      await composable.fetchSessions()

      expect(composable.activeSessions.value).toHaveLength(2)
      expect(composable.activeSessions.value.every(s => s.isActive)).toBe(true)
    })

    it('should identify current session correctly', async () => {
      const composable = useSessionManagement()

      // Mock sessions data
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1', isCurrent: false },
                { id: 'session-2', isCurrent: true },
                { id: 'session-3', isCurrent: false }
              ]
            }
          })
        })

      await composable.fetchSessions()

      expect(composable.currentSession.value).toBeDefined()
      expect(composable.currentSession.value.id).toBe('session-2')
      expect(composable.currentSession.value.isCurrent).toBe(true)
    })

    it('should compute other sessions correctly', async () => {
      const composable = useSessionManagement()

      // Mock sessions data
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1', isCurrent: true, isActive: true },
                { id: 'session-2', isCurrent: false, isActive: true },
                { id: 'session-3', isCurrent: false, isActive: false },
                { id: 'session-4', isCurrent: false, isActive: true }
              ]
            }
          })
        })

      await composable.fetchSessions()

      expect(composable.otherSessions.value).toHaveLength(2)
      expect(composable.otherSessions.value.every(s => !s.isCurrent && s.isActive)).toBe(true)
    })

    it('should compute session statistics correctly', async () => {
      const composable = useSessionManagement()

      // Mock sessions data
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1', isCurrent: true, isActive: true },
                { id: 'session-2', isCurrent: false, isActive: true },
                { id: 'session-3', isCurrent: false, isActive: false }
              ]
            }
          })
        })

      await composable.fetchSessions()

      const stats = composable.getSessionStats.value

      expect(stats.total).toBe(2) // Only active sessions
      expect(stats.active).toBe(2)
      expect(stats.current).toBe(1)
      expect(stats.others).toBe(1)
      expect(stats.hasMultiple).toBe(true)
    })
  })

  describe('utility functions', () => {
    it('should detect device type correctly', () => {
      const { formatSession } = useSessionManagement()

      const testCases = [
        { userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', expected: 'mobile' },
        { userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)', expected: 'tablet' },
        { userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', expected: 'desktop' },
        { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', expected: 'desktop' }
      ]

      testCases.forEach(({ userAgent, expected }) => {
        const session = { userAgent, id: 'test' }
        const formatted = formatSession(session)
        expect(formatted.deviceType).toBe(expected)
      })
    })

    it('should detect browser name correctly', () => {
      const { formatSession } = useSessionManagement()

      const testCases = [
        { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', expected: 'Chrome' },
        { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0', expected: 'Firefox' },
        { userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15', expected: 'Safari' },
        { userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59', expected: 'Edge' }
      ]

      testCases.forEach(({ userAgent, expected }) => {
        const session = { userAgent, id: 'test' }
        const formatted = formatSession(session)
        expect(formatted.browserName).toBe(expected)
      })
    })
  })

  describe('CSRF token handling', () => {
    it('should get CSRF token successfully', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { csrfToken: 'csrf-token-123' }
        })
      })

      const { fetchSessions } = useSessionManagement()

      // Mock sessions response
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { sessions: [] }
        })
      })

      await fetchSessions()

      // Should have called CSRF endpoint first
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include'
      })
    })

    it('should handle CSRF token errors gracefully', async () => {
      global.fetch
        .mockRejectedValueOnce(new Error('CSRF error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { sessions: [] }
          })
        })

      const { fetchSessions } = useSessionManagement()

      // Should still work even if CSRF token fails
      await fetchSessions()

      expect(global.fetch).toHaveBeenCalledTimes(2)
    })
  })

  describe('auto refresh', () => {
    it('should start auto refresh', () => {
      const { startAutoRefresh } = useSessionManagement()

      startAutoRefresh()

      // Should set up interval
      expect(vi.getTimerCount()).toBeGreaterThan(0)
    })

    it('should stop auto refresh', () => {
      const { startAutoRefresh, stopAutoRefresh } = useSessionManagement()

      startAutoRefresh()
      const timerCount = vi.getTimerCount()

      stopAutoRefresh()

      expect(vi.getTimerCount()).toBeLessThan(timerCount)
    })

    it('should refresh sessions automatically', async () => {
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { sessions: [] }
        })
      })

      const { startAutoRefresh } = useSessionManagement()

      startAutoRefresh()

      // Advance timer to trigger auto refresh
      vi.advanceTimersByTime(30000) // 30 seconds

      expect(global.fetch).toHaveBeenCalled()
    })
  })

  describe('error handling', () => {
    it('should clear errors', async () => {
      const { error, clearError, fetchSessions } = useSessionManagement()

      // First trigger an error by making fetchSessions fail
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockRejectedValueOnce(new Error('Test error'))

      await fetchSessions()
      expect(error.value).toBe('Test error')

      clearError()
      expect(error.value).toBeNull()
    })
  })

  describe('new session detection', () => {
    it('should detect new sessions', async () => {
      const composable = useSessionManagement()

      // First set up initial session
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [{ id: 'session-1' }]
            }
          })
        })

      await composable.fetchSessions()

      // Mock API response with 2 sessions for the check
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { csrfToken: 'csrf-token-123' }
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: {
              sessions: [
                { id: 'session-1' },
                { id: 'session-2' }
              ]
            }
          })
        })

      const result = await composable.checkForNewSessions()

      expect(result.newSessionDetected).toBe(true)
      expect(result.newSessionCount).toBe(1)
    })

    it('should handle no new sessions', async () => {
      const { sessions, checkForNewSessions } = useSessionManagement()

      // Start with 1 session
      sessions.value = [{ id: 'session-1' }]

      // Mock API response with same session
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            sessions: [{ id: 'session-1' }]
          }
        })
      })

      const result = await checkForNewSessions()

      expect(result.newSessionDetected).toBe(false)
      expect(result.newSessionCount).toBe(0)
    })
  })
})
