/**
 * Authentication Composable Unit Tests (TEST-002)
 * Tests for authentication composable functions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'

// Mock the auth store
const mockAuthStore = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  refreshToken: vi.fn(),
  requestPasswordReset: vi.fn(),
  resetPassword: vi.fn(),
  updateProfile: vi.fn(),
  changePassword: vi.fn(),
  initializeAuth: vi.fn(),
  clearError: vi.fn(),
}

vi.mock('../../../ui/stores/auth.js', () => ({
  useAuthStore: () => mockAuthStore,
}))

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  currentRoute: { value: { path: '/', query: {} } },
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRouter.currentRoute.value,
}))

// Create a mock useAuth composable for testing
const useAuth = () => {
  const authStore = mockAuthStore
  const router = mockRouter

  const login = async (credentials) => {
    const result = await authStore.login(credentials)
    if (result.success) {
      router.push('/')
    }
    return result
  }

  const register = async (userData) => {
    const result = await authStore.register(userData)
    if (result.success) {
      router.push('/login')
    }
    return result
  }

  const logout = async () => {
    await authStore.logout()
    router.push('/login')
  }

  const refreshToken = async () => {
    return await authStore.refreshToken()
  }

  const requestPasswordReset = async (email) => {
    return await authStore.requestPasswordReset(email)
  }

  const resetPassword = async (resetData) => {
    const result = await authStore.resetPassword(resetData)
    if (result.success) {
      router.push('/login')
    }
    return result
  }

  const updateProfile = async (profileData) => {
    return await authStore.updateProfile(profileData)
  }

  const changePassword = async (passwordData) => {
    return await authStore.changePassword(passwordData)
  }

  const requireAuth = () => {
    if (!authStore.isAuthenticated) {
      router.push('/login')
      return false
    }
    return true
  }

  const requireGuest = () => {
    if (authStore.isAuthenticated) {
      router.push('/')
      return false
    }
    return true
  }

  return {
    // State
    user: authStore.user,
    token: authStore.token,
    isAuthenticated: authStore.isAuthenticated,
    loading: authStore.loading,
    error: authStore.error,

    // Actions
    login,
    register,
    logout,
    refreshToken,
    requestPasswordReset,
    resetPassword,
    updateProfile,
    changePassword,
    requireAuth,
    requireGuest,
  }
}

describe('Authentication Composable (TEST-002)', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    mockAuthStore.user = null
    mockAuthStore.token = null
    mockAuthStore.isAuthenticated = false
    mockAuthStore.loading = false
    mockAuthStore.error = null
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  // Simple test to verify configuration works
  it('should be properly configured', () => {
    expect(true).toBe(true)
  })

  describe('State Access', () => {
    it('should provide access to authentication state', () => {
      const { user, token, isAuthenticated, loading, error } = useAuth()

      expect(user).toBeNull()
      expect(token).toBeNull()
      expect(isAuthenticated).toBe(false)
      expect(loading).toBe(false)
      expect(error).toBeNull()
    })

    it('should reflect changes in authentication state', () => {
      mockAuthStore.user = { id: '1', email: '<EMAIL>' }
      mockAuthStore.token = 'test-token'
      mockAuthStore.isAuthenticated = true

      const { user, token, isAuthenticated } = useAuth()

      expect(user).toEqual({ id: '1', email: '<EMAIL>' })
      expect(token).toBe('test-token')
      expect(isAuthenticated).toBe(true)
    })
  })

  describe('Login Function', () => {
    it('should login and redirect on success', async () => {
      const { login } = useAuth()
      const credentials = { email: '<EMAIL>', password: 'password123' }

      mockAuthStore.login.mockResolvedValue({ success: true })

      const result = await login(credentials)

      expect(mockAuthStore.login).toHaveBeenCalledWith(credentials)
      expect(mockRouter.push).toHaveBeenCalledWith('/')
      expect(result.success).toBe(true)
    })

    it('should not redirect on login failure', async () => {
      const { login } = useAuth()
      const credentials = { email: '<EMAIL>', password: 'wrongpassword' }

      mockAuthStore.login.mockResolvedValue({ success: false, error: 'Invalid credentials' })

      const result = await login(credentials)

      expect(mockAuthStore.login).toHaveBeenCalledWith(credentials)
      expect(mockRouter.push).not.toHaveBeenCalled()
      expect(result.success).toBe(false)
    })
  })

  describe('Register Function', () => {
    it('should register and redirect to login on success', async () => {
      const { register } = useAuth()
      const userData = { email: '<EMAIL>', password: 'password123', name: 'Test User' }

      mockAuthStore.register.mockResolvedValue({ success: true })

      const result = await register(userData)

      expect(mockAuthStore.register).toHaveBeenCalledWith(userData)
      expect(mockRouter.push).toHaveBeenCalledWith('/login')
      expect(result.success).toBe(true)
    })

    it('should not redirect on registration failure', async () => {
      const { register } = useAuth()
      const userData = { email: '<EMAIL>', password: '123' }

      mockAuthStore.register.mockResolvedValue({ success: false, error: 'Password too short' })

      const result = await register(userData)

      expect(mockRouter.push).not.toHaveBeenCalled()
      expect(result.success).toBe(false)
    })
  })

  describe('Logout Function', () => {
    it('should logout and redirect to login', async () => {
      const { logout } = useAuth()

      mockAuthStore.logout.mockResolvedValue()

      await logout()

      expect(mockAuthStore.logout).toHaveBeenCalled()
      expect(mockRouter.push).toHaveBeenCalledWith('/login')
    })
  })

  describe('Token Refresh Function', () => {
    it('should refresh token', async () => {
      const { refreshToken } = useAuth()

      mockAuthStore.refreshToken.mockResolvedValue({ success: true, token: 'new-token' })

      const result = await refreshToken()

      expect(mockAuthStore.refreshToken).toHaveBeenCalled()
      expect(result.success).toBe(true)
    })
  })

  describe('Password Reset Functions', () => {
    it('should request password reset', async () => {
      const { requestPasswordReset } = useAuth()
      const email = '<EMAIL>'

      mockAuthStore.requestPasswordReset.mockResolvedValue({ success: true })

      const result = await requestPasswordReset(email)

      expect(mockAuthStore.requestPasswordReset).toHaveBeenCalledWith(email)
      expect(result.success).toBe(true)
    })

    it('should reset password and redirect to login on success', async () => {
      const { resetPassword } = useAuth()
      const resetData = { token: 'reset-token', password: 'newpassword123' }

      mockAuthStore.resetPassword.mockResolvedValue({ success: true })

      const result = await resetPassword(resetData)

      expect(mockAuthStore.resetPassword).toHaveBeenCalledWith(resetData)
      expect(mockRouter.push).toHaveBeenCalledWith('/login')
      expect(result.success).toBe(true)
    })
  })

  describe('Profile Management Functions', () => {
    it('should update profile', async () => {
      const { updateProfile } = useAuth()
      const profileData = { name: 'Updated Name', email: '<EMAIL>' }

      mockAuthStore.updateProfile.mockResolvedValue({ success: true })

      const result = await updateProfile(profileData)

      expect(mockAuthStore.updateProfile).toHaveBeenCalledWith(profileData)
      expect(result.success).toBe(true)
    })

    it('should change password', async () => {
      const { changePassword } = useAuth()
      const passwordData = { currentPassword: 'oldpass', newPassword: 'newpass123' }

      mockAuthStore.changePassword.mockResolvedValue({ success: true })

      const result = await changePassword(passwordData)

      expect(mockAuthStore.changePassword).toHaveBeenCalledWith(passwordData)
      expect(result.success).toBe(true)
    })
  })

  describe('Route Guards', () => {
    it('should allow access when authenticated (requireAuth)', () => {
      mockAuthStore.isAuthenticated = true
      const { requireAuth } = useAuth()

      const result = requireAuth()

      expect(result).toBe(true)
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should redirect to login when not authenticated (requireAuth)', () => {
      mockAuthStore.isAuthenticated = false
      const { requireAuth } = useAuth()

      const result = requireAuth()

      expect(result).toBe(false)
      expect(mockRouter.push).toHaveBeenCalledWith('/login')
    })

    it('should allow access when not authenticated (requireGuest)', () => {
      mockAuthStore.isAuthenticated = false
      const { requireGuest } = useAuth()

      const result = requireGuest()

      expect(result).toBe(true)
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should redirect to home when authenticated (requireGuest)', () => {
      mockAuthStore.isAuthenticated = true
      const { requireGuest } = useAuth()

      const result = requireGuest()

      expect(result).toBe(false)
      expect(mockRouter.push).toHaveBeenCalledWith('/')
    })
  })
})
