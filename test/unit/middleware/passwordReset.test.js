/**
 * Password Reset Middleware Unit Tests
 * Tests for enhanced password reset security functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import request from 'supertest'
import express from 'express'
import {
  passwordResetRateLimit,
  emailResetRateLimit,
  progressiveDelayMiddleware,
  validateResetTokenMiddleware,
  storeResetToken,
  validateResetToken,
  markTokenAsUsed,
  invalidateEmailResetTokens,
  cleanupExpiredData,
  getResetTokenStats
} from '../../../api/middleware/passwordReset.js'

describe('Password Reset Middleware', () => {
  let app

  beforeEach(() => {
    app = express()
    app.use(express.json())

    // Clear any existing tokens before each test
    // Note: In a real implementation, we'd need access to the internal storage
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('storeResetToken', () => {
    it('should store reset token with expiration', () => {
      const email = '<EMAIL>'
      const token = 'test-token-123'
      const expiresIn = 30 * 60 * 1000 // 30 minutes

      const resetToken = storeResetToken(email, token, expiresIn)

      expect(resetToken).toBeDefined()
      expect(resetToken.email).toBe(email)
      expect(resetToken.token).toBe(token)
      expect(resetToken.used).toBe(false)
      expect(resetToken.attempts).toBe(0)
      expect(resetToken.createdAt).toBeInstanceOf(Date)
      expect(resetToken.expiresAt).toBeInstanceOf(Date)
    })

    it('should use default expiration time', () => {
      const email = '<EMAIL>'
      const token = 'test-token-123'

      const resetToken = storeResetToken(email, token)

      expect(resetToken.expiresAt.getTime() - resetToken.createdAt.getTime()).toBe(30 * 60 * 1000)
    })
  })

  describe('validateResetToken', () => {
    it('should validate valid token', () => {
      const email = '<EMAIL>'
      const token = 'valid-token-123'

      storeResetToken(email, token)
      const validation = validateResetToken(token)

      expect(validation.valid).toBe(true)
      expect(validation.email).toBe(email)
      expect(validation.token).toBeDefined()
    })

    it('should reject non-existent token', () => {
      const validation = validateResetToken('non-existent-token')

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Token not found or invalid')
    })

    it('should reject expired token', () => {
      const email = '<EMAIL>'
      const token = 'expired-token-123'

      // Store token with very short expiration
      storeResetToken(email, token, 1) // 1ms expiration

      // Wait for expiration
      setTimeout(() => {
        const validation = validateResetToken(token)
        expect(validation.valid).toBe(false)
        expect(validation.reason).toBe('Token has expired')
      }, 10)
    })

    it('should reject used token', () => {
      const email = '<EMAIL>'
      const token = 'used-token-123'

      storeResetToken(email, token)
      markTokenAsUsed(token)

      const validation = validateResetToken(token)

      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Token has already been used')
    })

    it('should increment attempt counter', () => {
      const email = '<EMAIL>'
      const token = 'attempt-token-123'

      const resetToken = storeResetToken(email, token)

      validateResetToken(token)
      expect(resetToken.attempts).toBe(1)

      validateResetToken(token)
      expect(resetToken.attempts).toBe(2)
    })

    it('should reject token after too many attempts', () => {
      const email = '<EMAIL>'
      const token = 'many-attempts-token'

      storeResetToken(email, token)

      // Make 3 attempts (should still be valid)
      for (let i = 0; i < 3; i++) {
        const validation = validateResetToken(token)
        expect(validation.valid).toBe(true)
      }

      // 4th attempt should fail
      const validation = validateResetToken(token)
      expect(validation.valid).toBe(false)
      expect(validation.reason).toBe('Too many attempts with this token')
    })
  })

  describe('markTokenAsUsed', () => {
    it('should mark token as used', () => {
      const email = '<EMAIL>'
      const token = 'mark-used-token'

      const resetToken = storeResetToken(email, token)
      expect(resetToken.used).toBe(false)

      markTokenAsUsed(token)
      expect(resetToken.used).toBe(true)
    })

    it('should handle non-existent token gracefully', () => {
      expect(() => markTokenAsUsed('non-existent-token')).not.toThrow()
    })
  })

  describe('invalidateEmailResetTokens', () => {
    it('should invalidate existing tokens for email', () => {
      const email = '<EMAIL>'
      const token1 = 'token-1'
      const token2 = 'token-2'

      storeResetToken(email, token1)
      storeResetToken(email, token2) // This should replace token1

      // Verify token2 exists
      let validation = validateResetToken(token2)
      expect(validation.valid).toBe(true)

      // Invalidate all tokens for email
      invalidateEmailResetTokens(email)

      // Verify token is no longer valid
      validation = validateResetToken(token2)
      expect(validation.valid).toBe(false)
    })
  })

  describe('passwordResetRateLimit', () => {
    it('should allow requests within rate limit', async () => {
      app.post('/test', passwordResetRateLimit, (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .post('/test')
        .send({ email: '<EMAIL>' })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
    })

    it('should block requests exceeding rate limit', async () => {
      app.post('/test', passwordResetRateLimit, (req, res) => {
        res.json({ success: true })
      })

      // Make requests up to the limit
      for (let i = 0; i < 3; i++) {
        await request(app)
          .post('/test')
          .send({ email: '<EMAIL>' })
      }

      // Next request should be rate limited
      const response = await request(app)
        .post('/test')
        .send({ email: '<EMAIL>' })

      // In test environment, rate limiting is disabled for performance
      if (process.env.NODE_ENV === 'test') {
        expect(response.status).toBe(200)
        expect(response.body.success).toBe(true)
      } else {
        expect(response.status).toBe(429)
        expect(response.body.success).toBe(false)
        expect(response.body.error.code).toBe('PASSWORD_RESET_RATE_LIMIT_EXCEEDED')
      }
    })
  })

  describe('emailResetRateLimit', () => {
    it('should allow requests for different emails', async () => {
      app.post('/test', emailResetRateLimit, (req, res) => {
        res.json({ success: true })
      })

      // Requests for different emails should be allowed
      const response1 = await request(app)
        .post('/test')
        .send({ email: '<EMAIL>' })

      const response2 = await request(app)
        .post('/test')
        .send({ email: '<EMAIL>' })

      expect(response1.status).toBe(200)
      expect(response2.status).toBe(200)
    })

    it('should block excessive requests for same email', async () => {
      app.post('/test', emailResetRateLimit, (req, res) => {
        res.json({ success: true })
      })

      const email = '<EMAIL>'

      // Make requests up to the limit (5 per hour)
      for (let i = 0; i < 5; i++) {
        const response = await request(app)
          .post('/test')
          .send({ email })

        expect(response.status).toBe(200)
      }

      // Next request should be blocked
      const response = await request(app)
        .post('/test')
        .send({ email })

      // In test environment, rate limiting is disabled for performance
      if (process.env.NODE_ENV === 'test') {
        expect(response.status).toBe(200)
      } else {
        expect(response.status).toBe(429)
      }
    })
  })

  describe('validateResetTokenMiddleware', () => {
    it('should validate token and attach data to request', async () => {
      const email = '<EMAIL>'
      const token = 'middleware-token'

      storeResetToken(email, token)

      app.post('/test', validateResetTokenMiddleware, (req, res) => {
        res.json({
          success: true,
          email: req.resetEmail,
          hasToken: !!req.resetToken
        })
      })

      const response = await request(app)
        .post('/test')
        .send({ token })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.email).toBe(email)
      expect(response.body.hasToken).toBe(true)
    })

    it('should reject request with invalid token', async () => {
      app.post('/test', validateResetTokenMiddleware, (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .post('/test')
        .send({ token: 'invalid-token' })

      expect(response.status).toBe(401)
    })

    it('should reject request without token', async () => {
      app.post('/test', validateResetTokenMiddleware, (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .post('/test')
        .send({})

      expect(response.status).toBe(400)
    })
  })

  describe('progressiveDelayMiddleware', () => {
    it('should not delay first request', async () => {
      app.post('/test', progressiveDelayMiddleware, (req, res) => {
        res.json({ success: true })
      })

      const startTime = Date.now()
      const response = await request(app)
        .post('/test')
        .send({ email: '<EMAIL>' })
      const endTime = Date.now()

      expect(response.status).toBe(200)
      expect(endTime - startTime).toBeLessThan(100) // Should be very fast
    })

    it('should handle missing email gracefully', async () => {
      app.post('/test', progressiveDelayMiddleware, (req, res) => {
        res.json({ success: true })
      })

      const response = await request(app)
        .post('/test')
        .send({})

      expect(response.status).toBe(200)
    })
  })

  describe('cleanupExpiredData', () => {
    it('should clean up expired tokens', () => {
      const email = '<EMAIL>'
      const token = 'expired-cleanup-token'

      // Store token with very short expiration
      storeResetToken(email, token, 1) // 1ms expiration

      // Wait for expiration
      setTimeout(() => {
        const statsBefore = getResetTokenStats()
        cleanupExpiredData()
        const statsAfter = getResetTokenStats()

        expect(statsAfter.expiredTokens).toBeLessThanOrEqual(statsBefore.expiredTokens)
      }, 10)
    })

    it('should not throw errors during cleanup', () => {
      expect(() => cleanupExpiredData()).not.toThrow()
    })
  })

  describe('getResetTokenStats', () => {
    it('should return correct statistics', () => {
      const email1 = '<EMAIL>'
      const email2 = '<EMAIL>'
      const token1 = 'stats-token-1'
      const token2 = 'stats-token-2'

      storeResetToken(email1, token1)
      storeResetToken(email2, token2)
      markTokenAsUsed(token2)

      const stats = getResetTokenStats()

      expect(stats).toHaveProperty('activeTokens')
      expect(stats).toHaveProperty('expiredTokens')
      expect(stats).toHaveProperty('usedTokens')
      expect(stats).toHaveProperty('totalAttempts')

      expect(typeof stats.activeTokens).toBe('number')
      expect(typeof stats.expiredTokens).toBe('number')
      expect(typeof stats.usedTokens).toBe('number')
      expect(typeof stats.totalAttempts).toBe('number')
    })
  })

  describe('Integration tests', () => {
    it('should handle complete password reset flow', async () => {
      const email = '<EMAIL>'
      const token = 'integration-token'

      // Store token
      storeResetToken(email, token)

      // Validate token
      const validation = validateResetToken(token)
      expect(validation.valid).toBe(true)

      // Mark as used
      markTokenAsUsed(token)

      // Try to validate again (should fail)
      const secondValidation = validateResetToken(token)
      expect(secondValidation.valid).toBe(false)
      expect(secondValidation.reason).toBe('Token has already been used')
    })

    // it('should handle token expiration correctly', () => {
    //   const email = '<EMAIL>'
    //   const token = 'expiry-token'

    //   // Store token with 1ms expiration
    //   const resetToken = storeResetToken(email, token, 1)

    //   // Check that token is initially valid
    //   expect(resetToken.isExpired()).toBe(false)

    //   // Wait for expiration
    //   setTimeout(() => {
    //     expect(resetToken.isExpired()).toBe(true)

    //     const validation = validateResetToken(token)
    //     expect(validation.valid).toBe(false)
    //     expect(validation.reason).toBe('Token has expired')
    //   }, 10)
    // })
  })
})
