# Authentication Test Suite

This directory contains comprehensive tests for the authentication system as specified in the authentication-requirements.md document.

## Test Structure

The test suite is organized according to the requirements (TEST-002 through TEST-010):

### TEST-001: Authentication Store Tests
- **Location**: `ui/stores/__tests__/auth.spec.js`
- **Description**: Unit tests for authentication store actions and mutations
- **Coverage**: Login, register, logout, token refresh, password reset, state management

### TEST-002: Authentication Composable Tests
- **Location**: `test/unit/composables/useAuth.test.js`
- **Description**: Tests for authentication composable functions
- **Coverage**: Composable API, reactive state, navigation helpers

### TEST-003: Route Guard Tests
- **Location**: `test/unit/router/guards.test.js`
- **Description**: Tests for route guard functionality
- **Coverage**: Protected routes, guest routes, admin routes, redirects

### TEST-004: Authentication API Middleware Tests
- **Location**: `test/unit/middleware/auth-middleware.test.js`
- **Description**: Tests for authentication API middleware
- **Coverage**: API calls, error handling, request/response processing

### TEST-005: Complete Authentication Flows
- **Location**: `test/integration/auth-flows.test.js`
- **Description**: Integration tests for complete authentication flows
- **Coverage**: Login flow, registration flow, logout flow, session management

### TEST-006: Token Refresh Mechanism Tests
- **Location**: `test/integration/token-refresh.test.js`
- **Description**: Tests for token refresh mechanism
- **Coverage**: Automatic refresh, refresh failures, concurrent requests

### TEST-007: Role-Based Access Control Tests
- **Location**: `test/integration/rbac.test.js`
- **Description**: Tests for role-based access control
- **Coverage**: Role checking, permission validation, access control

### TEST-008: Authentication User Journeys (E2E)
- **Location**: `test/e2e/auth-journeys.test.js`
- **Description**: End-to-end tests for authentication user journeys
- **Coverage**: Complete user flows, UI interactions, error scenarios

### TEST-009: Responsive Design Tests (E2E)
- **Location**: `test/e2e/responsive-design.test.js`
- **Description**: Tests for responsive design on different devices
- **Coverage**: Mobile, tablet, desktop layouts, touch interactions

### TEST-010: Error Handling and Edge Cases
- **Location**: `test/integration/error-handling.test.js`
- **Description**: Tests for error handling and edge cases
- **Coverage**: Network errors, validation errors, browser compatibility

## Running Tests

### Prerequisites
```bash
npm install
```

### Run All Tests
```bash
npm run test
```

### Run Specific Test Suites

#### Unit Tests Only
```bash
npm run test:unit
```

#### Integration Tests Only
```bash
npm run test:integration
```

#### E2E Tests Only
```bash
npm run test:e2e
```

#### Run Tests by Category
```bash
# Authentication store tests
npm run test -- ui/stores/__tests__/auth.spec.js

# Composable tests
npm run test -- test/unit/composables/useAuth.test.js

# Route guard tests
npm run test -- test/unit/router/guards.test.js

# API middleware tests
npm run test -- test/unit/middleware/auth-middleware.test.js

# Integration tests
npm run test -- test/integration/

# E2E tests
npm run test -- test/e2e/
```

### Test Coverage
```bash
npm run test:coverage
```

### Watch Mode
```bash
npm run test:watch
```

## Test Configuration

### Vitest Configuration
- **File**: `test/vitest.config.js`
- **Features**: Coverage reporting, browser testing, mock configuration

### Test Setup
- **Global Setup**: `test/setup/global-setup.js`
- **Test Setup**: `test/setup/test-setup.js`
- **Mocks**: Browser APIs, localStorage, fetch, Vue Router

## Test Utilities

### Mock Helpers
```javascript
import { createMockUser, createMockAuthResponse } from './setup/test-setup.js'

const mockUser = createMockUser({ role: 'admin' })
const mockResponse = createMockAuthResponse({ user: mockUser })
```

### Test Helpers
```javascript
import { waitForNextTick, flushPromises, mockTimers } from './setup/test-setup.js'

// Wait for Vue reactivity
await waitForNextTick()

// Wait for all promises
await flushPromises()

// Mock timers
const timers = mockTimers()
timers.advanceTimersByTime(1000)
timers.restore()
```

## Coverage Requirements

### Minimum Coverage Thresholds
- **Global**: 80% (branches, functions, lines, statements)
- **Authentication Store**: 90%
- **PocketBase Service**: 85%

### Coverage Reports
- **Text**: Console output
- **JSON**: `test/results/test-results.json`
- **HTML**: `test/results/test-results.html`

## Continuous Integration

### GitHub Actions
The test suite is designed to run in CI/CD pipelines with:
- Parallel test execution
- Coverage reporting
- Test result artifacts
- Failure notifications

### Test Environment
- **Node.js**: 18+
- **Browser**: Chromium (for E2E tests)
- **Database**: Mock PocketBase service

## Debugging Tests

### Debug Mode
```bash
npm run test:debug
```

### Browser DevTools (E2E)
```bash
npm run test:e2e -- --headed
```

### Verbose Output
```bash
npm run test -- --reporter=verbose
```

## Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### Mocking
- Mock external dependencies
- Use realistic mock data
- Reset mocks between tests

### Assertions
- Use specific assertions
- Test both success and failure cases
- Verify side effects

### Performance
- Keep tests fast and focused
- Use parallel execution
- Mock heavy operations

## Troubleshooting

### Common Issues

#### Tests Timing Out
- Increase timeout in vitest.config.js
- Check for unresolved promises
- Verify mock implementations

#### Mock Issues
- Ensure mocks are cleared between tests
- Check mock implementation matches real API
- Verify mock setup in beforeEach hooks

#### Coverage Issues
- Check file paths in coverage configuration
- Ensure test files are not included in coverage
- Verify source files are being tested

### Getting Help
- Check test logs for detailed error messages
- Use debug mode for step-by-step execution
- Review mock configurations and setup files

## Contributing

### Adding New Tests
1. Follow the existing test structure
2. Use appropriate test category (unit/integration/e2e)
3. Include both positive and negative test cases
4. Update this README if adding new test categories

### Test Standards
- Maintain high test coverage
- Write clear, descriptive test names
- Include edge cases and error scenarios
- Follow the established mocking patterns
