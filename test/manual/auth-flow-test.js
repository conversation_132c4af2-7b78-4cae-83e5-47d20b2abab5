#!/usr/bin/env node

/**
 * Manual Authentication Flow Test Script
 * Tests the complete authentication workflow against a running API server
 */

// Using built-in fetch (Node.js 18+)

const API_BASE_URL = 'http://localhost:3001/api'
const TEST_USER = {
  email: '<EMAIL>',
  password: 'admin123'
}

/**
 * Make HTTP request with error handling
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })

    const data = await response.json()

    return {
      status: response.status,
      headers: response.headers,
      data,
      ok: response.ok
    }
  } catch (error) {
    console.error(`Request failed: ${error.message}`)
    return {
      status: 0,
      data: { error: error.message },
      ok: false
    }
  }
}

/**
 * Extract CSRF token from response
 */
function extractCSRFToken(response) {
  if (response.data && response.data.data && response.data.data.csrfToken) {
    return response.data.data.csrfToken
  }
  return null
}

/**
 * Extract auth token from login response
 */
function extractAuthToken(response) {
  if (response.data && response.data.data && response.data.data.token) {
    return response.data.data.token
  }
  return null
}

/**
 * Test Step 1: Get CSRF Token
 */
async function testGetCSRFToken() {
  console.log('\n🔐 Step 1: Getting CSRF Token...')

  const response = await makeRequest(`${API_BASE_URL}/auth/csrf-token`)

  if (!response.ok) {
    console.error('❌ Failed to get CSRF token:', response.data)
    return null
  }

  const csrfToken = extractCSRFToken(response)
  if (!csrfToken) {
    console.error('❌ CSRF token not found in response:', response.data)
    return null
  }

  console.log('✅ CSRF token obtained:', csrfToken.substring(0, 16) + '...')
  return csrfToken
}

/**
 * Test Step 2: Login without CSRF token (should fail)
 */
async function testLoginWithoutCSRF() {
  console.log('\n🚫 Step 2: Testing login without CSRF token (should fail)...')

  const response = await makeRequest(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    body: JSON.stringify(TEST_USER)
  })

  if (response.status === 403) {
    console.log('✅ Login correctly rejected without CSRF token')
    console.log('   Error:', response.data.error?.message)
    return true
  } else {
    console.error('❌ Login should have been rejected without CSRF token')
    console.error('   Response:', response.data)
    return false
  }
}

/**
 * Test Step 3: Login with invalid CSRF token (should fail)
 */
async function testLoginWithInvalidCSRF() {
  console.log('\n🚫 Step 3: Testing login with invalid CSRF token (should fail)...')

  const response = await makeRequest(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      'X-CSRF-Token': 'invalid-token-12345'
    },
    body: JSON.stringify(TEST_USER)
  })

  if (response.status === 403) {
    console.log('✅ Login correctly rejected with invalid CSRF token')
    console.log('   Error:', response.data.error?.message)
    return true
  } else {
    console.error('❌ Login should have been rejected with invalid CSRF token')
    console.error('   Response:', response.data)
    return false
  }
}

/**
 * Test Step 4: Login with valid CSRF token (should succeed)
 */
async function testLoginWithValidCSRF(csrfToken) {
  console.log('\n✅ Step 4: Testing login with valid CSRF token...')

  const response = await makeRequest(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      'X-CSRF-Token': csrfToken
    },
    body: JSON.stringify(TEST_USER)
  })

  if (!response.ok) {
    console.error('❌ Login failed with valid CSRF token:', response.data)
    return null
  }

  const authToken = extractAuthToken(response)
  if (!authToken) {
    console.error('❌ Auth token not found in login response:', response.data)
    return null
  }

  console.log('✅ Login successful!')
  console.log('   User:', response.data.data.user?.email)
  console.log('   Token:', authToken.substring(0, 16) + '...')

  return authToken
}

/**
 * Test Step 5: Access protected endpoint with auth token
 */
async function testProtectedEndpoint(authToken) {
  console.log('\n🔒 Step 5: Testing protected endpoint access...')

  const response = await makeRequest(`${API_BASE_URL}/tasks`, {
    headers: {
      'Authorization': `Bearer ${authToken}`
    }
  })

  if (response.ok) {
    console.log('✅ Protected endpoint access successful')
    console.log('   Response:', response.data.success ? 'Success' : 'Failed')
    return true
  } else {
    console.error('❌ Protected endpoint access failed:', response.data)
    return false
  }
}

/**
 * Test Step 6: Access protected endpoint without auth token (should fail)
 */
async function testProtectedEndpointWithoutAuth() {
  console.log('\n🚫 Step 6: Testing protected endpoint without auth token (should fail)...')

  const response = await makeRequest(`${API_BASE_URL}/tasks`)

  if (response.status === 401) {
    console.log('✅ Protected endpoint correctly rejected without auth token')
    console.log('   Error:', response.data.error?.message)
    return true
  } else {
    console.error('❌ Protected endpoint should have been rejected without auth token')
    console.error('   Response:', response.data)
    return false
  }
}

/**
 * Main test runner
 */
async function runAuthFlowTests() {
  console.log('🧪 Starting Complete Authentication Flow Tests')
  console.log('=' .repeat(60))

  let passed = 0
  let total = 0

  try {
    // Step 1: Get CSRF Token
    total++
    const csrfToken = await testGetCSRFToken()
    if (csrfToken) passed++

    // Step 2: Test login without CSRF token
    total++
    if (await testLoginWithoutCSRF()) passed++

    // Step 3: Test login with invalid CSRF token
    total++
    if (await testLoginWithInvalidCSRF()) passed++

    // Step 4: Test login with valid CSRF token
    total++
    const authToken = await testLoginWithValidCSRF(csrfToken)
    if (authToken) passed++

    if (authToken) {
      // Step 5: Test protected endpoint with auth token
      total++
      if (await testProtectedEndpoint(authToken)) passed++
    }

    // Step 6: Test protected endpoint without auth token
    total++
    if (await testProtectedEndpointWithoutAuth()) passed++

  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message)
  }

  // Results
  console.log('\n' + '=' .repeat(60))
  console.log(`📊 Test Results: ${passed}/${total} tests passed`)

  if (passed === total) {
    console.log('🎉 All authentication flow tests passed!')
    process.exit(0)
  } else {
    console.log('❌ Some tests failed. Check the API server and configuration.')
    process.exit(1)
  }
}

// Check if API server is running
async function checkAPIServer() {
  console.log('🔍 Checking if API server is running...')

  try {
    const response = await fetch(`${API_BASE_URL}/auth/csrf-token`)
    if (response.ok) {
      console.log('✅ API server is running')
      return true
    }
  } catch (error) {
    console.error('❌ API server is not running or not accessible')
    console.error('   Please start the API server with: npm run api:dev')
    return false
  }

  return false
}

// Run tests
if (await checkAPIServer()) {
  await runAuthFlowTests()
} else {
  process.exit(1)
}
