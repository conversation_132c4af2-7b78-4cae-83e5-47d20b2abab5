@baseUrl = http://localhost:3001/api
@csrf_token =
@auth_token =

### Get CSRF token (required for authentication requests)
# @name csrfToken
GET {{baseUrl}}/auth/csrf-token

@csrf_token = {{csrfToken.response.body.data.csrfToken}}

### Establish authenticated session
# @name login
POST {{baseUrl}}/auth/login
Content-Type: application/json
X-CSRF-Token: {{csrf_token}}

{
    "email": "<EMAIL>",
    "password": "admin123",
    "rememberMe": false
}

### Test authenticated endpoint (use token from login response)

@auth_token = {{login.response.body.data.token}}

GET {{baseUrl}}/tasks
Authorization: Bearer {{auth_token}}
Content-Type: application/json

###

GET {{baseUrl}}/projects
Authorization: Bearer {{auth_token}}
Content-Type: application/json