/**
 * Complete Authentication Flow Integration Tests
 * Tests the end-to-end authentication workflow including CSRF token handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import request from 'supertest'
import { createApp, CONFIG } from '../../api/config/server.js'
import { authRoutes } from '../../api/routes/auth.js'
import { taskRoutes } from '../../api/routes/tasks.js'

// Mock the authentication service
vi.mock('../../api/services/authService.js', () => ({
  AuthService: {
    login: vi.fn().mockResolvedValue({
      success: true,
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user',
        verified: true
      },
      token: 'mock-jwt-token-12345',
      refreshToken: 'mock-refresh-token-12345',
      sessionId: 'mock-session-id'
    }),
    register: vi.fn().mockResolvedValue({
      success: true,
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        verified: false
      }
    }),
    logout: vi.fn().mockResolvedValue({
      success: true,
      message: 'Logout successful'
    })
  }
}))

// Mock the authentication middleware
vi.mock('../../api/middleware/auth.js', () => ({
  authenticate: (req, res, next) => {
    const authHeader = req.headers.authorization
    if (authHeader && authHeader.startsWith('Bearer ') && authHeader.includes('mock-jwt-token')) {
      req.user = {
        id: 'test-user-id',
        email: '<EMAIL>',
        role: 'user'
      }
      next()
    } else {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString()
        }
      })
    }
  },
  optionalAuthenticate: (req, res, next) => next(),
  requireAdmin: (req, res, next) => {
    if (req.user && req.user.role === 'admin') {
      next()
    } else {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Admin access required',
          timestamp: new Date().toISOString()
        }
      })
    }
  }
}))

// Mock the database service
vi.mock('../../common/services/database.js', () => ({
  databaseService: Promise.resolve({
    getAllTasks: vi.fn().mockResolvedValue([]),
    getTaskById: vi.fn().mockResolvedValue(null),
    createTask: vi.fn().mockResolvedValue({ id: 'test-task-id' }),
    updateTask: vi.fn().mockResolvedValue({ id: 'test-task-id' }),
    deleteTask: vi.fn().mockResolvedValue(true)
  })
}))

// Mock the session service
vi.mock('../../api/services/sessionService.js', () => ({
  SessionService: {
    createSession: vi.fn().mockReturnValue({
      id: 'mock-session-id',
      userId: 'test-user-id',
      token: 'mock-jwt-token-12345',
      lastActivity: new Date(),
      isActive: true
    }),
    getSessionByToken: vi.fn().mockReturnValue({
      id: 'mock-session-id',
      userId: 'test-user-id',
      token: 'mock-jwt-token-12345',
      lastActivity: new Date(),
      isActive: true
    }),
    updateSessionActivity: vi.fn().mockReturnValue(true),
    invalidateSession: vi.fn().mockReturnValue(true)
  }
}))

describe('Complete Authentication Flow Integration Tests', () => {
  let app
  let server

  beforeEach(async () => {
    // Create Express app with all middleware
    app = createApp()

    // Register routes
    app.use(`${CONFIG.API_PREFIX}/auth`, authRoutes)

    // Add a simple mock tasks endpoint for testing (instead of full tasks routes)
    app.get(`${CONFIG.API_PREFIX}/tasks`, (req, res) => {
      res.status(200).json({
        success: true,
        data: {
          tasks: [],
          total: 0,
          page: 1,
          limit: 10
        },
        message: 'Tasks retrieved successfully'
      })
    })

    // Add 404 handler
    app.use((req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Endpoint not found',
          timestamp: new Date().toISOString()
        }
      })
    })

    // Global error handler
    app.use((error, req, res, next) => {
      res.status(error.statusCode || 500).json({
        success: false,
        error: {
          code: error.code || 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Internal server error',
          timestamp: new Date().toISOString()
        }
      })
    })
  })

  afterEach(() => {
    if (server) {
      server.close()
    }
    vi.clearAllMocks()
  })

  describe('CSRF Token Endpoint', () => {
    it('should provide CSRF token via GET /api/auth/csrf-token', async () => {
      const response = await request(app)
        .get('/api/auth/csrf-token')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toBeDefined()
      expect(response.body.data.csrfToken).toBeDefined()
      expect(typeof response.body.data.csrfToken).toBe('string')
      expect(response.body.data.csrfToken.length).toBeGreaterThan(0)

      // Should also set CSRF token in cookie
      expect(response.headers['set-cookie']).toBeDefined()
      const cookieHeader = response.headers['set-cookie'].find(cookie =>
        cookie.includes('csrf-token=')
      )
      expect(cookieHeader).toBeDefined()
    })

    it('should return same token for same session', async () => {
      const agent = request.agent(app)

      const response1 = await agent
        .get('/api/auth/csrf-token')
        .expect(200)

      // Wait a bit to ensure we're not hitting timing issues
      await new Promise(resolve => setTimeout(resolve, 10))

      const response2 = await agent
        .get('/api/auth/csrf-token')
        .expect(200)

      // Debug output
      console.log('Token 1:', response1.body.data.csrfToken?.substring(0, 16))
      console.log('Token 2:', response2.body.data.csrfToken?.substring(0, 16))

      expect(response1.body.data.csrfToken).toBe(response2.body.data.csrfToken)
    })
  })

  describe('Complete Authentication Flow', () => {
    it('should complete full authentication workflow', async () => {
      const agent = request.agent(app)

      // Step 1: Get CSRF token
      const csrfResponse = await agent
        .get('/api/auth/csrf-token')
        .expect(200)

      const csrfToken = csrfResponse.body.data.csrfToken
      expect(csrfToken).toBeDefined()

      // Step 2: Attempt login without CSRF token (should fail)
      await agent
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: false
        })
        .expect(403)

      // Step 3: Login with CSRF token (should succeed with mocked auth service)
      const loginResponse = await agent
        .post('/api/auth/login')
        .set('X-CSRF-Token', csrfToken)
        .send({
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: false
        })
        .expect(200)

      expect(loginResponse.body.success).toBe(true)
      expect(loginResponse.body.data).toBeDefined()
      expect(loginResponse.body.data.token).toBeDefined()
      expect(loginResponse.body.data.user).toBeDefined()
      expect(loginResponse.body.message).toBe('Login successful')

      // Step 4: Use auth token for protected endpoint
      const authToken = loginResponse.body.data.token

      const protectedResponse = await agent
        .get('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(protectedResponse.body.success).toBe(true)
    })

    it('should reject login with invalid CSRF token', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('X-CSRF-Token', 'invalid-token')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: false
        })
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toContain('CSRF')
    })

    it('should reject login without CSRF token', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: false
        })
        .expect(403)

      expect(response.body.success).toBe(false)
      expect(response.body.error.message).toBe('CSRF token missing')
    })
  })

  describe('Token Response Format', () => {
    it('should return properly formatted authentication token', async () => {
      const agent = request.agent(app)

      // Get CSRF token
      const csrfResponse = await agent.get('/api/auth/csrf-token')
      const csrfToken = csrfResponse.body.data.csrfToken

      // Login
      const loginResponse = await agent
        .post('/api/auth/login')
        .set('X-CSRF-Token', csrfToken)
        .send({
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: false
        })
        .expect(200)

      // Verify token format
      const { data } = loginResponse.body
      expect(data.token).toBeDefined()
      expect(typeof data.token).toBe('string')
      expect(data.token.length).toBeGreaterThan(0)

      // Verify refresh token
      expect(data.refreshToken).toBeDefined()
      expect(typeof data.refreshToken).toBe('string')

      // Verify user data
      expect(data.user).toBeDefined()
      expect(data.user.id).toBeDefined()
      expect(data.user.email).toBeDefined()
    })
  })

  describe('Error Handling', () => {
    it('should provide clear error messages for CSRF failures', async () => {
      const testCases = [
        {
          name: 'missing CSRF token',
          headers: {},
          expectedMessage: 'CSRF token missing'
        },
        {
          name: 'invalid CSRF token',
          headers: { 'X-CSRF-Token': 'invalid-token' },
          expectedMessage: 'Invalid CSRF token'
        }
      ]

      for (const testCase of testCases) {
        const response = await request(app)
          .post('/api/auth/login')
          .set(testCase.headers)
          .send({
            email: '<EMAIL>',
            password: 'password123'
          })
          .expect(403)

        expect(response.body.success).toBe(false)
        expect(response.body.error).toBeDefined()
        expect(response.body.error.message).toBe(testCase.expectedMessage)
        expect(response.body.error.code).toBe('FORBIDDEN')
        expect(response.body.error.timestamp).toBeDefined()
      }
    })

    it('should handle CSRF token endpoint errors gracefully', async () => {
      // Mock an error in CSRF token generation
      const originalConsoleError = console.error
      console.error = vi.fn()

      // This test verifies the error handling structure is in place
      const response = await request(app)
        .get('/api/auth/csrf-token')
        .expect(200) // Should still work with current implementation

      expect(response.body.success).toBe(true)

      console.error = originalConsoleError
    })
  })

  describe('Session Management Integration', () => {
    it('should handle session extension with proper authentication', async () => {
      const agent = request.agent(app)

      // Get CSRF token and login
      const csrfResponse = await agent.get('/api/auth/csrf-token')
      const csrfToken = csrfResponse.body.data.csrfToken

      const loginResponse = await agent
        .post('/api/auth/login')
        .set('X-CSRF-Token', csrfToken)
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200)

      const authToken = loginResponse.body.data.token

      // Test session extension
      const extendResponse = await agent
        .post('/api/auth/extend-session')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(extendResponse.body.success).toBe(true)
      expect(extendResponse.body.message).toBe('Session extended successfully')
      expect(extendResponse.body.data.extendedAt).toBeDefined()
    })

    it('should get session status with proper authentication', async () => {
      const agent = request.agent(app)

      // Get CSRF token and login
      const csrfResponse = await agent.get('/api/auth/csrf-token')
      const csrfToken = csrfResponse.body.data.csrfToken

      const loginResponse = await agent
        .post('/api/auth/login')
        .set('X-CSRF-Token', csrfToken)
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200)

      const authToken = loginResponse.body.data.token

      // Test session status
      const statusResponse = await agent
        .get('/api/auth/session-status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(statusResponse.body.success).toBe(true)
      expect(statusResponse.body.data).toBeDefined()
      expect(statusResponse.body.data.sessionId).toBeDefined()
      expect(statusResponse.body.data.timeRemaining).toBeDefined()
      expect(statusResponse.body.data.isNearTimeout).toBeDefined()
    })
  })
})
