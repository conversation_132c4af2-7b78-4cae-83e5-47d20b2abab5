/**
 * Authentication Flows Integration Tests (TEST-005)
 * Tests for complete authentication flows (login, register, logout)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useAuthStore } from '../../ui/stores/auth.js'

// Mock the httpClient
vi.mock('../../ui/utils/httpClient.js', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}))

// Import the mocked httpClient after mocking
import httpClient from '../../ui/utils/httpClient.js'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  currentRoute: { value: { path: '/', query: {} } },
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRouter.currentRoute.value,
}))

describe('Authentication Flows Integration Tests (TEST-005)', () => {
  let authStore

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    // Reset all mock functions
    Object.values(httpClient).forEach(mockFn => {
      if (typeof mockFn === 'function') {
        mockFn.mockReset()
      }
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Complete Login Flow', () => {
    it('should complete full login flow successfully', async () => {
      // Mock successful login response
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' }
      const mockToken = 'jwt-token-123'

      httpClient.post.mockResolvedValue({
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token-123'
        }
      })

      // Step 1: Initial state should be unauthenticated
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()

      // Step 2: Attempt login
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const result = await authStore.login(credentials)

      // Step 3: Verify login was successful
      expect(result.success).toBe(true)
      expect(httpClient.post).toHaveBeenCalledWith('/auth/login', {
        email: credentials.email,
        password: credentials.password,
        rememberMe: false
      })

      // Step 4: Verify state was updated
      expect(authStore.isAuthenticated).toBe(true)
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.token).toBe(mockToken)
      expect(authStore.error).toBeNull()

      // Step 5: Verify persistence
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', mockToken)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_user', JSON.stringify(mockUser))
    })

    it('should handle login failure gracefully', async () => {
      // Mock failed login response
      const mockError = {
        errorType: 'AUTHENTICATION_ERROR',
        message: 'Invalid credentials',
        statusCode: 401
      }

      httpClient.post.mockRejectedValue(mockError)

      // Step 1: Attempt login with invalid credentials
      const credentials = { email: '<EMAIL>', password: 'wrongpassword' }
      const result = await authStore.login(credentials)

      // Step 2: Verify login failed
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid email or password')

      // Step 3: Verify state remains unauthenticated
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.error).toBe('Invalid email or password')

      // Step 4: Verify no persistence occurred
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })

    it('should handle network errors during login', async () => {
      // Mock network error
      const mockError = {
        errorType: 'NETWORK_ERROR',
        message: 'Network error'
      }

      httpClient.post.mockRejectedValue(mockError)

      // Step 1: Attempt login
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const result = await authStore.login(credentials)

      // Step 2: Verify error handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Network error. Please check your connection.')
      expect(authStore.isAuthenticated).toBe(false)
    })
  })

  describe('Complete Registration Flow', () => {
    it('should complete full registration flow successfully', async () => {
      // Mock successful registration response
      const mockUser = { id: '1', email: '<EMAIL>', name: 'New User' }

      httpClient.post.mockResolvedValue({
        success: true,
        data: { user: mockUser },
        message: 'Registration successful'
      })

      // Step 1: Attempt registration
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
      }
      const result = await authStore.register(userData)

      // Step 2: Verify registration was successful
      expect(result.success).toBe(true)
      expect(httpClient.post).toHaveBeenCalledWith('/auth/register', userData)
      expect(authStore.error).toBeNull()
    })

    it('should handle registration validation errors', async () => {
      // Mock validation error response
      httpClient.post.mockRejectedValue({
        statusCode: 400,
        errorType: 'VALIDATION_ERROR',
        message: 'Email already exists'
      })

      // Step 1: Attempt registration with existing email
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }
      const result = await authStore.register(userData)

      // Step 2: Verify error handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Email already exists')
    })
  })

  describe('Complete Logout Flow', () => {
    it('should complete full logout flow successfully', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'jwt-token-123'
      authStore.isAuthenticated = true

      // Mock successful logout
      httpClient.post.mockResolvedValue({
        success: true,
        message: 'Logout successful'
      })

      // Step 2: Perform logout
      await authStore.logout()

      // Step 3: Verify logout was called
      expect(httpClient.post).toHaveBeenCalledWith('/auth/logout')

      // Step 4: Verify state was cleared
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)

      // Step 5: Verify localStorage was cleared
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
    })

    it('should handle logout errors gracefully', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'jwt-token-123'
      authStore.isAuthenticated = true

      // Mock logout error
      httpClient.post.mockRejectedValue({
        statusCode: 500,
        errorType: 'SERVER_ERROR',
        message: 'Logout failed'
      })

      // Step 2: Perform logout
      await authStore.logout()

      // Step 3: Verify state was still cleared despite error
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
    })
  })

  describe('Password Reset Flow', () => {
    it('should complete password reset request flow', async () => {
      // Mock successful password reset request
      httpClient.post.mockResolvedValue({
        success: true,
        message: 'Password reset email sent'
      })

      // Step 1: Request password reset
      const email = '<EMAIL>'
      const result = await authStore.requestPasswordReset(email)

      // Step 2: Verify request was successful
      expect(result.success).toBe(true)
      expect(httpClient.post).toHaveBeenCalledWith('/auth/forgot-password', { email })
    })

    it('should complete password reset confirmation flow', async () => {
      // Mock successful password reset
      httpClient.post.mockResolvedValue({
        success: true,
        message: 'Password reset successful'
      })

      // Step 1: Reset password with token
      const resetData = { token: 'reset-token-123', password: 'newpassword123', passwordConfirm: 'newpassword123' }
      const result = await authStore.resetPassword(resetData)

      // Step 2: Verify reset was successful
      expect(result.success).toBe(true)
      expect(httpClient.post).toHaveBeenCalledWith('/auth/reset-password', resetData)
    })
  })

  describe('Session Restoration Flow', () => {
    it('should restore session from localStorage on initialization', async () => {
      // Step 1: Mock stored auth data
      const mockUser = { id: '1', email: '<EMAIL>' }
      const mockToken = 'stored-token-123'

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return mockToken
        if (key === 'auth_user') return JSON.stringify(mockUser)
        return null
      })

      // Mock successful user profile fetch
      httpClient.get.mockResolvedValue({
        success: true,
        data: { user: mockUser }
      })

      // Step 2: Initialize auth
      await authStore.initializeAuth()

      // Step 3: Verify session was restored
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.token).toBe(mockToken)
      expect(authStore.isAuthenticated).toBe(true)
      expect(authStore.initialized).toBe(true)
    })

    it('should handle invalid stored session gracefully', async () => {
      // Step 1: Mock invalid stored data
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'invalid-token'
        if (key === 'auth_user') return 'invalid-json'
        return null
      })

      // Mock failed user profile fetch (invalid token)
      httpClient.get.mockRejectedValue({
        statusCode: 401,
        errorType: 'AUTHENTICATION_ERROR',
        message: 'Invalid token'
      })

      // Step 2: Initialize auth
      await authStore.initializeAuth()

      // Step 3: Verify session was not restored
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.initialized).toBe(true)
    })
  })

  describe('Profile Management Flow', () => {
    it('should complete profile update flow', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>', name: 'Old Name' }
      authStore.isAuthenticated = true

      // Mock successful profile update
      const updatedUser = { id: '1', email: '<EMAIL>', name: 'New Name' }
      httpClient.put.mockResolvedValue({
        success: true,
        data: { user: updatedUser },
        message: 'Profile updated successfully'
      })

      // Step 2: Update profile
      const profileData = { name: 'New Name' }
      const result = await authStore.updateProfile(profileData)

      // Step 3: Verify update was successful
      expect(result.success).toBe(true)
      expect(httpClient.put).toHaveBeenCalledWith('/auth/profile', profileData)
    })

    it('should complete password change flow', async () => {
      // Step 1: Set up authenticated state
      authStore.isAuthenticated = true

      // Mock successful password change
      httpClient.put.mockResolvedValue({
        success: true,
        message: 'Password changed successfully'
      })

      // Step 2: Change password
      const passwordData = { currentPassword: 'oldpass', newPassword: 'newpass123', passwordConfirm: 'newpass123' }
      const result = await authStore.changePassword(passwordData)

      // Step 3: Verify change was successful
      expect(result.success).toBe(true)
      expect(httpClient.put).toHaveBeenCalledWith('/auth/password', passwordData)
    })
  })
})
