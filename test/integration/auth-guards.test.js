/**
 * Authentication Guards Integration Tests
 * Tests for comprehensive authentication guard functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'

// Mock the auth store
const mockAuthStore = {
  isAuthenticated: false,
  user: null,
  initialized: false,
  initializeAuth: vi.fn(),
  clearAuthState: vi.fn(),
}

vi.mock('../../ui/stores/auth.js', () => ({
  useAuthStore: () => mockAuthStore,
}))

// Mock route objects
const createMockRoute = (path, query = {}) => ({
  path,
  name: path.replace('/', '') || 'home',
  fullPath: path + (Object.keys(query).length ? `?${new URLSearchParams(query).toString()}` : ''),
  query,
  params: {},
})

const createMockNext = () => {
  const next = vi.fn()
  next.mockImplementation((to) => {
    if (to) {
      next.calledWith = to
    }
  })
  return next
}

// Import the router guard logic
const authGuard = async (to, from, next) => {
  const authStore = mockAuthStore

  // Initialize auth store if not already initialized
  if (!authStore.initialized) {
    try {
      await authStore.initializeAuth()
    } catch (error) {
      console.warn('Auth initialization failed:', error)
      authStore.clearAuthState()
    }
  }

  const isAuthenticated = authStore.isAuthenticated
  const guestOnlyRoutes = ['/login', '/register', '/forgot-password', '/reset-password']
  const publicRoutes = ['/help']
  
  const isGuestOnlyRoute = guestOnlyRoutes.some(route => to.path.startsWith(route))
  const isPublicRoute = publicRoutes.some(route => to.path.startsWith(route))
  const requiresAuth = !isGuestOnlyRoute && !isPublicRoute

  // Redirect authenticated users away from guest-only routes
  if (isAuthenticated && isGuestOnlyRoute) {
    const redirectTo = to.query.redirect || '/dashboard'
    next(redirectTo)
    return
  }

  // Redirect unauthenticated users away from protected routes
  if (!isAuthenticated && requiresAuth) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // Allow navigation
  next()
}

describe('Authentication Guards Integration Tests', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    mockAuthStore.isAuthenticated = false
    mockAuthStore.user = null
    mockAuthStore.initialized = false
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Protected Routes', () => {
    const protectedRoutes = [
      '/',
      '/dashboard',
      '/projects',
      '/tasks',
      '/tasks/123',
      '/upload',
      '/profile',
      '/settings'
    ]

    protectedRoutes.forEach(route => {
      it(`should redirect unauthenticated users from ${route} to login`, async () => {
        const to = createMockRoute(route)
        const from = createMockRoute('/login')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = false
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith({
          path: '/login',
          query: { redirect: route }
        })
      })

      it(`should allow authenticated users to access ${route}`, async () => {
        const to = createMockRoute(route)
        const from = createMockRoute('/dashboard')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = true
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith()
      })
    })
  })

  describe('Guest-Only Routes', () => {
    const guestOnlyRoutes = ['/login', '/register', '/forgot-password', '/reset-password']

    guestOnlyRoutes.forEach(route => {
      it(`should allow unauthenticated users to access ${route}`, async () => {
        const to = createMockRoute(route)
        const from = createMockRoute('/')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = false
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith()
      })

      it(`should redirect authenticated users from ${route} to dashboard`, async () => {
        const to = createMockRoute(route)
        const from = createMockRoute('/dashboard')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = true
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith('/dashboard')
      })

      it(`should redirect authenticated users from ${route} to original destination`, async () => {
        const to = createMockRoute(route, { redirect: '/projects' })
        const from = createMockRoute('/dashboard')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = true
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith('/projects')
      })
    })
  })

  describe('Public Routes', () => {
    const publicRoutes = ['/help']

    publicRoutes.forEach(route => {
      it(`should allow unauthenticated users to access ${route}`, async () => {
        const to = createMockRoute(route)
        const from = createMockRoute('/')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = false
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith()
      })

      it(`should allow authenticated users to access ${route}`, async () => {
        const to = createMockRoute(route)
        const from = createMockRoute('/dashboard')
        const next = createMockNext()

        mockAuthStore.isAuthenticated = true
        mockAuthStore.initialized = true

        await authGuard(to, from, next)

        expect(next).toHaveBeenCalledWith()
      })
    })
  })

  describe('Authentication Initialization', () => {
    it('should initialize auth store if not initialized', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.initialized = false
      mockAuthStore.initializeAuth.mockResolvedValue({ success: true })

      await authGuard(to, from, next)

      expect(mockAuthStore.initializeAuth).toHaveBeenCalled()
    })

    it('should handle auth initialization failure gracefully', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.initialized = false
      mockAuthStore.initializeAuth.mockRejectedValue(new Error('Network error'))

      await authGuard(to, from, next)

      expect(mockAuthStore.initializeAuth).toHaveBeenCalled()
      expect(mockAuthStore.clearAuthState).toHaveBeenCalled()
    })

    it('should not initialize auth store if already initialized', async () => {
      const to = createMockRoute('/')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.initialized = true
      mockAuthStore.isAuthenticated = true

      await authGuard(to, from, next)

      expect(mockAuthStore.initializeAuth).not.toHaveBeenCalled()
      expect(next).toHaveBeenCalledWith()
    })
  })

  describe('Redirect Preservation', () => {
    it('should preserve redirect query parameter when redirecting to login', async () => {
      const to = createMockRoute('/projects')
      const from = createMockRoute('/login')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({
        path: '/login',
        query: { redirect: '/projects' }
      })
    })

    it('should handle complex paths with query parameters', async () => {
      const to = createMockRoute('/tasks/123', { filter: 'active', sort: 'date' })
      const from = createMockRoute('/')
      const next = createMockNext()

      mockAuthStore.isAuthenticated = false
      mockAuthStore.initialized = true

      await authGuard(to, from, next)

      expect(next).toHaveBeenCalledWith({
        path: '/login',
        query: { redirect: '/tasks/123?filter=active&sort=date' }
      })
    })
  })
})
